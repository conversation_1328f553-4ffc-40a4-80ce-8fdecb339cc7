# Test Data Structure for INBOUND and SHIPMENT Dialogs

This document shows the expected data structure for testing the custom renderers and dialogs.

## Sample Row Data Structure

```javascript
{
  // Other row properties...
  "INBOUND": "180000107,180000108,180000109",
  "SHIPMENT": "No.136/IMIP-JKT/05/2025,No.137/IMIP-JKT/05/2025",
  "DATA_INBOUND": [
    {
      "INBOUND_NO": "180000107",
      "INBOUND_ITEM": "Item 001",
      "PO_NO": "PO-2025-001",
      "PO_ITEM": "10",
      "DOC_DATE": "2025-05-27",
      "DELIV_DATE": "2025-05-31",
      "USER": "IMLOG14",
      "CREATE_DATE": "2025-05-28",
      "DATA_SHIPMENT": [
        {
          "SHIPMENT_NO": "No.136/IMIP-JKT/05/2025",
          "SHIPMENT_ITEM": "Item 001-A",
          "SHIPMENT_DATE": "2025-06-01",
          "PENGIRIM": "MARIO",
          "STATUS": "IN-PROCESS",
          "ROUTE": "JAKARTA-KENDARI",
          "ROUTE_TYPE": "DIRECT"
        }
      ]
    },
    {
      "INBOUND_NO": "180000108",
      "INBOUND_ITEM": "Item 002",
      "PO_NO": "PO-2025-002",
      "PO_ITEM": "20",
      "DOC_DATE": "2025-05-27",
      "DELIV_DATE": "2025-05-31",
      "USER": "IMLOG14",
      "CREATE_DATE": "2025-05-28",
      "DATA_SHIPMENT": [
        {
          "SHIPMENT_NO": "No.137/IMIP-JKT/05/2025",
          "SHIPMENT_ITEM": "Item 002-A",
          "SHIPMENT_DATE": "2025-06-01",
          "PENGIRIM": "MARIO",
          "STATUS": "IN-PROCESS",
          "ROUTE": "JAKARTA-KENDARI",
          "ROUTE_TYPE": "DIRECT"
        }
      ]
    },
    {
      "INBOUND_NO": "180000109",
      "INBOUND_ITEM": "Item 003",
      "PO_NO": "PO-2025-003",
      "PO_ITEM": "30",
      "DOC_DATE": "2025-05-27",
      "DELIV_DATE": "2025-05-31",
      "USER": "IMLOG14",
      "CREATE_DATE": "2025-05-28",
      "DATA_SHIPMENT": []
    }
  ]
}
```

## How the Renderers Work

### INBOUND Column Renderer
1. Parses the comma-separated `INBOUND` field: "180000107,180000108,180000109"
2. Creates 3 clickable buttons: [180000107] [180000108] [180000109]
3. When clicked, finds the matching inbound data from `DATA_INBOUND` array
4. Opens InboundDialog with the found data

### SHIPMENT Column Renderer
1. Parses the comma-separated `SHIPMENT` field: "No.136/IMIP-JKT/05/2025,No.137/IMIP-JKT/05/2025"
2. Creates 2 clickable buttons: [No.136/IMIP-JKT/05/2025] [No.137/IMIP-JKT/05/2025]
3. When clicked, searches through all `DATA_INBOUND[].DATA_SHIPMENT` arrays
4. Opens ShipmentDialog with the found shipment data and related inbound info

## Dialog Content Mapping

### InboundDialog Fields
- INBOUND_NO → LIKP-BLDAT
- INBOUND_ITEM → (Custom field)
- PO_NO → (Purchase Order Number)
- PO_ITEM → (Purchase Order Item)
- DOC_DATE → LIKP-BLDAT
- DELIV_DATE → LIKP-LFDAT
- USER → LIKP-ERNAM
- CREATE_DATE → LIKP-ERDAT

### ShipmentDialog Fields
- SHIPMENT_NO → (Shipment Number)
- SHIPMENT_ITEM → (Shipment Item)
- SHIPMENT_DATE → ZMMT014-ZSHIP_DATE
- PENGIRIM → ZMMT014-ZPENGIRIM
- STATUS → ZMMT014-ZSTATUS_SHIP
- ROUTE → ZMMT014-ZROUTE
- ROUTE_TYPE → ZMMT014-ZROUTE_TYPE

## Testing Steps

1. Load reservation data with the above structure
2. Verify INBOUND column shows 3 buttons
3. Verify SHIPMENT column shows 2 buttons
4. Click each button to test dialog opening
5. Verify correct data is displayed in dialogs
6. Test dialog closing functionality
