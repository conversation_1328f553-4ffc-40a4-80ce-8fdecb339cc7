<template>
  <DialogForm
    ref="dialogForm"
    :max-width="800"
    :dialog-title="dialogTitle"
  >
    <template #content>
      <div class="pa-4">
        <v-card v-if="inboundData" class="mb-4">
          <v-card-title class="text-h6 primary white--text">
            Inbound Details - {{ inboundData.INBOUND_NO }}
          </v-card-title>
          <v-card-text>
            <v-row>
              <v-col cols="12" md="6">
                <v-list dense>
                  <v-list-item>
                    <v-list-item-content>
                      <v-list-item-title class="font-weight-bold">Inbound No:</v-list-item-title>
                      <v-list-item-subtitle>{{ inboundData.INBOUND_NO || '-' }}</v-list-item-subtitle>
                    </v-list-item-content>
                  </v-list-item>
                  <v-list-item>
                    <v-list-item-content>
                      <v-list-item-title class="font-weight-bold">Inbound Item:</v-list-item-title>
                      <v-list-item-subtitle>{{ inboundData.INBOUND_ITEM || '-' }}</v-list-item-subtitle>
                    </v-list-item-content>
                  </v-list-item>
                  <v-list-item>
                    <v-list-item-content>
                      <v-list-item-title class="font-weight-bold">PO No:</v-list-item-title>
                      <v-list-item-subtitle>{{ inboundData.PO_NO || '-' }}</v-list-item-subtitle>
                    </v-list-item-content>
                  </v-list-item>
                  <v-list-item>
                    <v-list-item-content>
                      <v-list-item-title class="font-weight-bold">PO Item:</v-list-item-title>
                      <v-list-item-subtitle>{{ inboundData.PO_ITEM || '-' }}</v-list-item-subtitle>
                    </v-list-item-content>
                  </v-list-item>
                </v-list>
              </v-col>
              <v-col cols="12" md="6">
                <v-list dense>
                  <v-list-item>
                    <v-list-item-content>
                      <v-list-item-title class="font-weight-bold">Doc Date:</v-list-item-title>
                      <v-list-item-subtitle>{{ formatDate(inboundData.DOC_DATE) || '-' }}</v-list-item-subtitle>
                    </v-list-item-content>
                  </v-list-item>
                  <v-list-item>
                    <v-list-item-content>
                      <v-list-item-title class="font-weight-bold">Delivery Date:</v-list-item-title>
                      <v-list-item-subtitle>{{ formatDate(inboundData.DELIV_DATE) || '-' }}</v-list-item-subtitle>
                    </v-list-item-content>
                  </v-list-item>
                  <v-list-item>
                    <v-list-item-content>
                      <v-list-item-title class="font-weight-bold">User:</v-list-item-title>
                      <v-list-item-subtitle>{{ inboundData.USER || '-' }}</v-list-item-subtitle>
                    </v-list-item-content>
                  </v-list-item>
                  <v-list-item>
                    <v-list-item-content>
                      <v-list-item-title class="font-weight-bold">Create Date:</v-list-item-title>
                      <v-list-item-subtitle>{{ formatDate(inboundData.CREATE_DATE) || '-' }}</v-list-item-subtitle>
                    </v-list-item-content>
                  </v-list-item>
                </v-list>
              </v-col>
            </v-row>
          </v-card-text>
        </v-card>

        <!-- Shipments Section -->
        <v-card v-if="inboundData && inboundData.DATA_SHIPMENT && inboundData.DATA_SHIPMENT.length > 0">
          <v-card-title class="text-h6 secondary white--text">
            Related Shipments
          </v-card-title>
          <v-card-text>
            <v-expansion-panels>
              <v-expansion-panel
                v-for="(shipment, index) in inboundData.DATA_SHIPMENT"
                :key="index"
              >
                <v-expansion-panel-header>
                  <strong>{{ shipment.SHIPMENT_NO || 'Shipment ' + (index + 1) }}</strong>
                  <template v-slot:actions>
                    <v-chip
                      :color="getStatusColor(shipment.STATUS)"
                      small
                      text-color="white"
                    >
                      {{ shipment.STATUS || 'Unknown' }}
                    </v-chip>
                  </template>
                </v-expansion-panel-header>
                <v-expansion-panel-content>
                  <v-row>
                    <v-col cols="12" md="6">
                      <v-list dense>
                        <v-list-item>
                          <v-list-item-content>
                            <v-list-item-title class="font-weight-bold">Shipment No:</v-list-item-title>
                            <v-list-item-subtitle>{{ shipment.SHIPMENT_NO || '-' }}</v-list-item-subtitle>
                          </v-list-item-content>
                        </v-list-item>
                        <v-list-item>
                          <v-list-item-content>
                            <v-list-item-title class="font-weight-bold">Shipment Item:</v-list-item-title>
                            <v-list-item-subtitle>{{ shipment.SHIPMENT_ITEM || '-' }}</v-list-item-subtitle>
                          </v-list-item-content>
                        </v-list-item>
                        <v-list-item>
                          <v-list-item-content>
                            <v-list-item-title class="font-weight-bold">Shipment Date:</v-list-item-title>
                            <v-list-item-subtitle>{{ formatDate(shipment.SHIPMENT_DATE) || '-' }}</v-list-item-subtitle>
                          </v-list-item-content>
                        </v-list-item>
                      </v-list>
                    </v-col>
                    <v-col cols="12" md="6">
                      <v-list dense>
                        <v-list-item>
                          <v-list-item-content>
                            <v-list-item-title class="font-weight-bold">Sender:</v-list-item-title>
                            <v-list-item-subtitle>{{ shipment.PENGIRIM || '-' }}</v-list-item-subtitle>
                          </v-list-item-content>
                        </v-list-item>
                        <v-list-item>
                          <v-list-item-content>
                            <v-list-item-title class="font-weight-bold">Route:</v-list-item-title>
                            <v-list-item-subtitle>{{ shipment.ROUTE || '-' }}</v-list-item-subtitle>
                          </v-list-item-content>
                        </v-list-item>
                        <v-list-item>
                          <v-list-item-content>
                            <v-list-item-title class="font-weight-bold">Route Type:</v-list-item-title>
                            <v-list-item-subtitle>{{ shipment.ROUTE_TYPE || '-' }}</v-list-item-subtitle>
                          </v-list-item-content>
                        </v-list-item>
                      </v-list>
                    </v-col>
                  </v-row>
                </v-expansion-panel-content>
              </v-expansion-panel>
            </v-expansion-panels>
          </v-card-text>
        </v-card>

        <v-alert v-if="!inboundData" type="warning" class="mt-4">
          No inbound data found for the selected item.
        </v-alert>
      </div>
    </template>
  </DialogForm>
</template>

<script lang="ts">
export default {
  name: "InboundDialog",
  data() {
    return {
      dialogTitle: "Inbound Details",
      inboundData: null,
    };
  },
  methods: {
    openDialog(rowData, inboundNo) {
      this.dialogTitle = `Inbound Details - ${inboundNo}`;
      this.inboundData = null;
      
      // Find the inbound data from DATA_INBOUND array
      if (rowData && rowData.DATA_INBOUND && Array.isArray(rowData.DATA_INBOUND)) {
        this.inboundData = rowData.DATA_INBOUND.find(
          inbound => inbound.INBOUND_NO === inboundNo
        );
      }
      
      this.$refs.dialogForm.openDialog();
    },

    closeDialog() {
      this.$refs.dialogForm.closeDialog();
    },

    formatDate(dateString) {
      if (!dateString) return '-';
      try {
        const date = new Date(dateString);
        return date.toLocaleDateString('en-GB', {
          day: '2-digit',
          month: '2-digit',
          year: 'numeric'
        });
      } catch (error) {
        return dateString;
      }
    },

    getStatusColor(status) {
      switch (status?.toUpperCase()) {
        case 'COMPLETED':
        case 'DELIVERED':
          return 'success';
        case 'IN-PROCESS':
        case 'PROCESSING':
          return 'warning';
        case 'PENDING':
          return 'info';
        case 'CANCELLED':
        case 'FAILED':
          return 'error';
        default:
          return 'grey';
      }
    },
  },
};
</script>

<style scoped>
.v-list-item-title {
  font-size: 0.875rem;
}
.v-list-item-subtitle {
  font-size: 0.875rem;
  color: rgba(0, 0, 0, 0.87) !important;
}
</style>
