<template>
  <v-dialog
    v-model="dialog"
    fullscreen
    hide-overlay
    persistent
    transition="dialog-top-transition"
    scrollable
    no-click-animation
    :retain-focus="false"
  >
    <v-card tile>
      <v-card-title class="d-flex">
        <v-toolbar-title>
          <v-btn
            size="small"
            ref="icon"
            icon="mdi-progress-pencil"
            variant="flat"
          >
          </v-btn>
          <span v-text="title"></span>
        </v-toolbar-title>
        <v-spacer></v-spacer>

        <small
          >Approval Status :
          <v-chip label :color="statusColor(form.AppStatus)" size="small" dark
            >{{ form.AppStatus }}
          </v-chip>
        </small>

        <v-btn
          v-if="form.ApprovalStatus === 'Y'"
          color="primary"
          size="small"
          variant="flat"
          class="ml-2"
          prepend-icon="mdi-printer"
          @click="printDocument"
          >Print</v-btn
        >

        <v-spacer></v-spacer>

        <v-btn
          icon
          density="compact"
          dark
          color="error"
          size="small"
          variant="flat"
          @click="close"
        >
          <v-icon>mdi-close</v-icon>
        </v-btn>
      </v-card-title>
      <v-divider />

      <v-card-text class="pl-1 pr-1">
        <v-form>
          <v-row wrap>
            <!--<v-col cols="12"
              v-if='message'
              xs12
            >
              <div class='red darken-2 text-xs-center'>
                <span class='white--text'>{{ message }}</span>
              </div>
            </v-col>
           <v-col cols="12"
              v-if='success'
              xs12
            >
              <div class='green darken-2 text-xs-center'>
                <span class='white--text'>{{ success }}</span>
              </div>
            </v-col> -->

            <v-col cols="12" md="12" class="d-flex">
              <v-row wrap class="pa-2">
                <v-col cols="12" md="6" sm="12" class="pa-0">
                  <v-row no-gutters class="pt-1">
                    <v-col cols="4" sm="3" md="2" class="pt-1">
                      <!-- <label>Company</label> -->
                      <FormFieldTitle title="Company" />
                    </v-col>
                    <v-col cols="8" sm="9" md="4" lg="4" class="pr-md-2 pt-1">
                      <v-autocomplete
                        v-if="form.ApprovalStatus === '-'"
                        v-model="form.CompanyName"
                        clearable
                        :items="itemCompany"
                        placeholder="Company"
                        :readonly="isReadOnly"
                        variant="outlined"
                        density="compact"
                        hide-details="auto"
                        @update:modelValue="changeCompany()"
                      ></v-autocomplete>
                      <v-text-field
                        v-else
                        v-model="form.CompanyName"
                        readonly
                        variant="outlined"
                        density="compact"
                        hide-details="auto"
                      ></v-text-field>
                    </v-col>

                    <v-col cols="4" sm="3" md="2" class="pt-1">
                      <FormFieldTitle title="Warehouse" />
                      <!-- <label>Warehouse</label> -->
                    </v-col>
                    <v-col cols="8" sm="9" md="4" lg="4" class="pt-1">
                      <v-autocomplete
                        v-if="form.ApprovalStatus === '-'"
                        v-model="form.WhsCode"
                        clearable
                        :items="itemUserWhs"
                        :loading="loadingChangeCompany"
                        placeholder="Warehouse"
                        item-title="name"
                        item-value="code"
                        :readonly="isReadOnly"
                        variant="outlined"
                        density="compact"
                        hide-details="auto"
                        @update:modelValue="changeWhs()"
                      ></v-autocomplete>
                      <v-text-field
                        v-else
                        v-model="form.WhsCode"
                        readonly
                        variant="outlined"
                        density="compact"
                        hide-details="auto"
                      ></v-text-field>
                    </v-col>
                  </v-row>

                  <v-row no-gutters class="pt-1">
                    <v-col cols="4" sm="3" md="2" class="pt-1">
                      <FormFieldTitle title="Requester" />
                      <!-- <label>Requester</label> -->
                    </v-col>
                    <v-col cols="8" sm="9" md="4" lg="4" class="pr-md-2 pt-1">
                      <v-autocomplete
                        v-if="form.ApprovalStatus === '-'"
                        v-model="form.U_NIK"
                        clearable
                        :items="itemUserNIK"
                        filled
                        placeholder="Requester"
                        item-title="U_UserName"
                        item-value="U_NIK"
                        :readonly="isReadOnly"
                        density="compact"
                        variant="outlined"
                        hide-details="auto"
                        @update:modelValue="changeDivision()"
                      >
                        <template v-slot:item="{ props, item }">
                          <v-list-item v-bind="props">
                            <v-list-item-title
                              v-text="item.raw.U_NIK"
                            ></v-list-item-title>
                          </v-list-item>
                        </template>
                      </v-autocomplete>
                      <v-text-field
                        v-else
                        v-model="form.RequesterName"
                        readonly
                        variant="outlined"
                        density="compact"
                        hide-details="auto"
                      ></v-text-field>
                    </v-col>

                    <v-col cols="4" sm="3" md="2" class="pt-1">
                      <FormFieldTitle title="Division" />
                      <!-- <label>Division</label> -->
                    </v-col>
                    <v-col cols="8" sm="9" md="4" lg="4" class="pt-1">
                      <v-autocomplete
                        v-if="form.ApprovalStatus === '-'"
                        v-model="form.Division"
                        :items="itemDivision"
                        clearable
                        filled
                        placeholder="Division"
                        density="compact"
                        variant="outlined"
                        hide-details="auto"
                        @update:modelValue="
                          getDataUsage(form.Division, $auth.user.location);
                          getDataCostCenter(form.Division, $auth.user.location);
                        "
                      ></v-autocomplete>
                      <v-text-field
                        v-else
                        v-model="form.Division"
                        readonly
                        variant="outlined"
                        density="compact"
                        hide-details="auto"
                      ></v-text-field>
                      <!--  <v-text-field
                        v-model='form.Division'
                        readonly
                        placeholder='Division'
                        variant="outlined" density="compact"
                        hide-details='auto'
                      ></v-text-field> -->
                    </v-col>
                  </v-row>

                  <v-row no-gutters class="pt-1">
                    <v-col cols="4" sm="3" md="2" class="pt-1">
                      <FormFieldTitle title="Request Type" />
                      <!-- <label>Request Type</label> -->
                    </v-col>
                    <v-col cols="8" sm="9" md="4" lg="4" class="pr-md-2 pt-1">
                      <v-autocomplete
                        v-model="form.RequestType"
                        :items="itemReqType"
                        clearable
                        :readonly="isReadOnly"
                        placeholder="Request Type"
                        variant="outlined"
                        density="compact"
                        hide-details="auto"
                        @update:modelValue="fillCheckbox()"
                      ></v-autocomplete>
                    </v-col>

                    <v-col cols="4" sm="3" md="2" class="pt-1">
                      <FormFieldTitle title="Category Type" />
                      <!-- <label>Category Type</label> -->
                    </v-col>
                    <v-col cols="8" sm="9" md="4" lg="4" class="pt-1">
                      <v-autocomplete
                        v-model="form.CategoryType"
                        :items="itemCategoryType"
                        clearable
                        :readonly="isReadOnly"
                        placeholder="Category Type"
                        variant="outlined"
                        density="compact"
                        hide-details="auto"
                        @update:modelValue="changeCategoryType()"
                      ></v-autocomplete>
                    </v-col>

                    <v-col
                      v-if="form.RequestType === 'Restock wh'"
                      cols="4"
                      sm="3"
                      md="2"
                      class="pt-1"
                    >
                      <FormFieldTitle title="Wh Transfer To" />
                      <!-- <label>Wh Transfer To</label> -->
                    </v-col>
                    <v-col
                      v-if="form.RequestType === 'Restock wh'"
                      cols="8"
                      sm="9"
                      md="4"
                      lg="4"
                      class="pt-1"
                    >
                      <v-autocomplete
                        v-model="form.WhTo"
                        :items="itemUserWhs"
                        clearable
                        :readonly="isReadOnly"
                        placeholder="Wh Transfer To"
                        item-title="name"
                        item-value="code"
                        variant="outlined"
                        density="compact"
                        hide-details="auto"
                      ></v-autocomplete>
                    </v-col>
                  </v-row>

                  <v-row no-gutters class="pt-1">
                    <v-col
                      v-if="form.RequestType === 'Sales'"
                      cols="4"
                      sm="3"
                      md="2"
                      class="pt-1"
                    >
                      <FormFieldTitle title="Usage For" />
                      <!-- <label>Usage For</label> -->
                    </v-col>
                    <v-col
                      v-if="form.RequestType === 'Sales'"
                      cols="8"
                      sm="9"
                      md="4"
                      lg="4"
                      class="pr-md-2 pt-1"
                    >
                      <v-autocomplete
                        v-model="form.UsageFor"
                        :items="itemUsageFor"
                        clearable
                        :readonly="isReadOnly"
                        placeholder="Usage For"
                        variant="outlined"
                        density="compact"
                        hide-details="auto"
                        @update:modelValue="changeUsageFor()"
                      ></v-autocomplete>
                    </v-col>

                    <v-col
                      v-if="
                        form.UsageFor === 'External' &&
                        form.RequestType === 'Sales'
                      "
                      cols="4"
                      sm="3"
                      md="2"
                      class="pt-1"
                    >
                      <FormFieldTitle title="Customer" />
                      <!-- <label>Customer</label> -->
                    </v-col>
                    <v-col
                      v-if="
                        form.UsageFor === 'External' &&
                        form.RequestType === 'Sales'
                      "
                      cols="8"
                      sm="9"
                      md="4"
                      lg="4"
                      class="pt-1"
                    >
                      <v-autocomplete
                        v-model="form.Customer"
                        :items="itemCustomer"
                        clearable
                        :readonly="isReadOnly"
                        placeholder="Customer"
                        item-title="CardName"
                        item-value="CardName"
                        variant="outlined"
                        density="compact"
                        hide-details="auto"
                      ></v-autocomplete>
                    </v-col>
                  </v-row>

                  <v-row v-if="form.ItemType === 'Non Ready Stock'" no-gutters>
                    <v-col cols="4" sm="3" md="2">
                      <FormFieldTitle title="Is Urgent?" />
                      <!-- <label>Is Urgent?</label> -->
                    </v-col>
                    <v-col cols="8" sm="9" md="4" lg="4" class="pr-md-2 pt-1">
                      <v-checkbox
                        v-model="form.Is_Urgent"
                        value="Yes"
                        density="compact"
                        hide-details="auto"
                        class="mt-n2"
                      ></v-checkbox>
                    </v-col>

                    <v-col
                      v-if="form.Is_Urgent === 'Yes'"
                      cols="4"
                      sm="3"
                      md="2"
                      class="pt-1"
                    >
                      <label>Urgent Reason</label>
                    </v-col>

                    <v-col
                      v-if="form.Is_Urgent === 'Yes'"
                      cols="8"
                      sm="9"
                      md="4"
                      lg="4"
                      class="pt-1"
                    >
                      <v-text-field
                        v-model="form.UrgentReason"
                        placeholder="Urgent Reason"
                        variant="outlined"
                        density="compact"
                        hide-details="auto"
                      ></v-text-field>
                    </v-col>
                  </v-row>

                  <v-row
                    v-if="
                      form.ItemType == 'Ready Stock' ||
                      form.ItemType == 'Non Ready Stock' ||
                      form.ItemType == 'Service' ||
                      form.ItemType == 'Asset'
                    "
                    no-gutters
                  >
                    <v-col cols="4" sm="3" md="2">
                      <FormFieldTitle title="Movement Type" />
                      <!-- <label>Movement Type</label> -->
                    </v-col>
                    <v-col cols="8" sm="9" md="4" lg="4" class="pr-md-2 pt-1">
                      <v-autocomplete
                        v-model="form.Usage"
                        clearable
                        :items="itemDataUsage"
                        placeholder="Usage"
                        item-title="description"
                        item-value="movement_type"
                        variant="outlined"
                        density="compact"
                        hide-details="auto"
                      ></v-autocomplete>
                    </v-col>

                    <v-col cols="4" sm="3" md="2" class="pt-1">
                      <label>Cost Center</label>
                    </v-col>

                    <v-col cols="8" sm="9" md="4" lg="4" class="pt-1">
                      <v-autocomplete
                        v-model="form.CostCenter"
                        clearable
                        :items="itemCostCenter"
                        placeholder="Cost Center"
                        item-title="cc_name"
                        item-value="cc_code"
                        variant="outlined"
                        density="compact"
                        hide-details="auto"
                      ></v-autocomplete>
                    </v-col>
                  </v-row>
                </v-col>

                <v-col
                  cols="12"
                  md="1"
                  sm="12"
                  class="hidden-sm-and-down"
                ></v-col>

                <v-col cols="12" md="5" sm="12" class="pa-0">
                  <v-row no-gutters>
                    <v-col cols="4" sm="3" md="2" class="pt-1">
                      <FormFieldTitle title="Item Type" />
                      <!-- <label>Item Type</label> -->
                    </v-col>
                    <v-col cols="8" sm="9" md="4" lg="4" class="pr-md-2 pt-1">
                      <v-autocomplete
                        v-model="form.ItemType"
                        clearable
                        :items="ItemType"
                        placeholder="Item Type"
                        variant="outlined"
                        density="compact"
                        hide-details="auto"
                        :readonly="status === 'update' || isReadOnly"
                        @update:modelValue="changeTable"
                      ></v-autocomplete>
                    </v-col>

                    <v-col cols="4" sm="3" md="2" class="pt-1">
                      <FormFieldTitle title="DocNum" />
                      <!-- <label>No</label> -->
                    </v-col>
                    <v-col cols="8" sm="9" md="4" lg="4" class="pt-1">
                      <v-text-field
                        v-model="form.DocNum"
                        readonly
                        placeholder="NO"
                        variant="outlined"
                        density="compact"
                        hide-details="auto"
                      ></v-text-field>
                    </v-col>
                  </v-row>

                  <v-row
                    v-if="form.CategoryType === 'Fuel'"
                    no-gutters
                    class="mt-1"
                  >
                    <v-col cols="4" sm="3" md="2" class="pt-1">
                      <FormFieldTitle title="Vehicle No" />
                      <!-- <label>Vehicle No</label> -->
                    </v-col>
                    <v-col cols="8" sm="9" md="4" lg="4" class="pr-md-2 pt-1">
                      <v-combobox
                        v-model="form.VehicleNo"
                        :items="itemVehicleNo"
                        placeholder="Vehicle No"
                        variant="outlined"
                        density="compact"
                        hide-details="auto"
                      ></v-combobox>
                    </v-col>

                    <v-col cols="4" sm="3" md="2" class="pt-1">
                      <FormFieldTitle title="Mileage" />
                      <!-- <label>Mileage</label> -->
                    </v-col>
                    <v-col cols="8" sm="9" md="4" lg="4" class="pt-1">
                      <v-text-field
                        v-model="form.Mileage"
                        placeholder="Mileage"
                        variant="outlined"
                        density="compact"
                        hide-details="auto"
                      ></v-text-field>
                    </v-col>
                  </v-row>

                  <v-row v-if="form.CategoryType === 'APD'" no-gutters>
                    <v-col cols="4" sm="3" md="2" class="pt-1">
                      <FormFieldTitle title="Replacement" />
                      <!-- <label>Replacement</label> -->
                    </v-col>
                    <v-col cols="8" sm="9" md="4" lg="4" class="pr-md-2 pt-1">
                      <v-combobox
                        v-model="form.Replacement"
                        :items="['Normal', 'Loss', 'Damage']"
                        placeholder="Replacement"
                        variant="outlined"
                        density="compact"
                        hide-details="auto"
                      ></v-combobox>
                    </v-col>
                  </v-row>

                  <v-row no-gutters class="pt-1">
                    <v-col cols="4" sm="3" md="2" class="pt-1">
                      <FormFieldTitle title="Request Date" />
                      <!-- <label>Request Date</label> -->
                    </v-col>
                    <v-col cols="8" sm="9" md="4" lg="4" class="pr-md-2 pt-1">
                      <v-text-field
                        v-model="form.DocDate"
                        placeholder="Request Date"
                        persistent-hint
                        variant="outlined"
                        density="compact"
                        hide-details="auto"
                        type="date"
                        no-title
                        @update:modelValue="changeDate"
                      ></v-text-field>
                    </v-col>

                    <v-col cols="4" sm="3" md="2" class="pt-1">
                      <FormFieldTitle title="Required Date" />
                      <!-- <label>Required Date</label> -->
                    </v-col>
                    <v-col cols="8" sm="9" md="4" lg="4" class="pt-1">
                      <v-text-field
                        v-model="form.RequiredDate"
                        placeholder="Required Date"
                        persistent-hint
                        variant="outlined"
                        density="compact"
                        hide-details="auto"
                        type="date"
                      ></v-text-field>
                    </v-col>
                  </v-row>

                  <v-row no-gutters>
                    <v-col cols="4" sm="3" md="2" class="pt-1">
                      <FormFieldTitle title="Doc Type" />
                      <!-- <label>Doc Type</label> -->
                    </v-col>
                    <v-col cols="8" sm="9" md="4" lg="4" class="pr-md-2 pt-1">
                      <v-autocomplete
                        v-model="form.DocumentType"
                        clearable
                        :items="itemDocumentType"
                        :readonly="isReadOnly"
                        placeholder="Document Type"
                        variant="outlined"
                        density="compact"
                        hide-details="auto"
                        @update:modelValue="changeTable"
                      ></v-autocomplete>
                    </v-col>

                    <v-col
                      v-if="form.CategoryType === 'APD'"
                      cols="4"
                      sm="3"
                      md="2"
                      class="pt-1"
                    >
                      <FormFieldTitle title="Employee Type" />
                      <!-- <label>Employee Type</label> -->
                    </v-col>
                    <v-col
                      v-if="form.CategoryType === 'APD'"
                      cols="8"
                      sm="9"
                      md="4"
                      lg="4"
                      class="pt-1"
                    >
                      <v-autocomplete
                        v-model="form.EmployeeType"
                        clearable
                        :items="itemEmployeeType"
                        placeholder="Employee Type"
                        variant="outlined"
                        density="compact"
                        hide-details="auto"
                      ></v-autocomplete>
                    </v-col>
                  </v-row>

                  <v-row no-gutters>
                    <v-col
                      v-if="
                        (form.ItemType === 'Ready Stock' ||
                          form.ItemType === 'Non Ready Stock') &&
                        checkCostType()
                      "
                      cols="4"
                      sm="3"
                      md="2"
                      class="pt-1"
                    >
                      <FormFieldTitle title="Cost Type" />
                      <!-- <label>Cost Type</label> -->
                    </v-col>
                    <v-col
                      v-if="
                        (form.ItemType === 'Ready Stock' ||
                          form.ItemType === 'Non Ready Stock') &&
                        checkCostType()
                      "
                      cols="8"
                      sm="9"
                      md="4"
                      lg="4"
                      class="pr-md-2 pt-1"
                    >
                      <v-autocomplete
                        v-model="form.CostType"
                        clearable
                        :items="itemCostType"
                        placeholder="Cost Type"
                        variant="outlined"
                        density="compact"
                        hide-details="auto"
                      ></v-autocomplete>
                    </v-col>

                    <v-col cols="4" sm="3" md="2" class="pt-1">
                      <FormFieldTitle title="Approval" />
                      <!-- <label>Approval</label> -->
                    </v-col>
                    <v-col cols="8" sm="9" md="4" lg="4" class="pr-md-2 pt-1">
                      <v-btn
                        v-if="form.ApprovalStatus !== '-'"
                        color="primary"
                        class="font-weight-bold text-right"
                        size="small"
                        variant="flat"
                        @click="$refs.approvalStages.openApprovalStages(form)"
                      >
                        Stages
                      </v-btn>
                    </v-col>
                  </v-row>
                </v-col>
              </v-row>
            </v-col>

            <LazyResvApprovalDetails
              ref="approvalStages"
            ></LazyResvApprovalDetails>

            <v-col cols="12" md="12" class="d-flex">
              <v-row wrap></v-row>
            </v-col>

            <div class="scroll-container-dialog">
              <!--Details -->
              <ResvReservationDetails
                ref="childDetails"
                :form="form"
                :is-data-set="isDataSet"
                @openDialog="openDialog"
                @openDialogAsset="openDialogAsset"
                @openDialogUom="openDialogUom"
                @openDialogOrderId="openDialogOrderId"
                @getData="getData"
                @openDialogLastResv="openDialogLastResv"
                @openDeleteRow="openDeleteRow"
                @openAttachmentDetails="openAttachmentDetails"
                @openDialogInbound="openDialogInbound"
                @openDialogShipment="openDialogShipment"
                @closeDialog="closeDialog"
                @checkCount="checkCount"
              ></ResvReservationDetails>
            </div>
            <v-col cols="12" md="5" class="pa-2">
              <v-row no-gutters class="pt-1">
                <v-col cols="1">
                  <FormFieldTitle title="Notes" />
                  <!-- <label>Notes</label> -->
                </v-col>
                <v-col cols="11">
                  <v-textarea
                    v-model="form.Memo"
                    placeholder="Notes"
                    variant="outlined"
                    rows="2"
                    class="ml-2"
                    density="compact"
                    hide-details="auto"
                  ></v-textarea>
                </v-col>
              </v-row>
            </v-col>
          </v-row>
        </v-form>
      </v-card-text>

      <v-card-actions>
        <v-tooltip top style="margin-top: 4px">
          <template v-slot:activator="{ props }">
            <v-btn
              size="small"
              v-bind="props"
              :disabled="isDisable"
              color="primary darken-1"
              variant="flat"
              prepend-icon="mdi-plus-circle-outline"
              @click="addLine()"
            >
              Add Line
            </v-btn>
          </template>
          <span>Add Line</span>
        </v-tooltip>

        <v-tooltip top class="ml-2" style="margin-top: 4px">
          <template v-slot:activator="{ props }">
            <v-badge
              :disabled="status === 'add'"
              :content="count_attachment"
              color="primary"
              :value="count_attachment"
              overlap
            >
              <v-btn
                v-bind="props"
                dark
                color="warning darken-1"
                icon="mdi-attachment"
                @click="openAttachment()"
              >
              </v-btn>
            </v-badge>
          </template>
          <span>Add Attachment</span>
        </v-tooltip>

        <!-- <v-tooltip
          top
          style='margin-top: 4px'
          class='ml-2'
        >
          <template #activator='{ on, attrs }'>
            <v-btn
             size="small"
              density="compact"
              v-bind='attrs'
              color='blue darken-1'
              class='white--text ml-2'
              v-on='on'
              @click='$refs.childDetails.exportCsv()'
            >
              Export CSV
            </v-btn>
          </template>
          <span>Export CSV</span>
        </v-tooltip> -->

        <v-spacer></v-spacer>

        <v-btn
          size="small"
          :loading="submitLoad"
          elevation="0"
          :disabled="$hasRole(['User Can ReUpdate']) ? false : isDisable"
          variant="flat"
          color="blue"
          class="white--text"
          prepend-icon="mdi-file-document"
          @click="save('all', false)"
        >
          {{ status === "add" ? "Save As Draft" : "Update Draft" }}
        </v-btn>

        <v-tooltip v-if="status === 'update'" top style="margin-top: 4px">
          <template v-slot:activator="{ props }">
            <v-btn
              v-if="status === 'update'"
              size="small"
              v-bind="props"
              :disabled="isDisable"
              variant="flat"
              color="success"
              class="white--text ml-1"
              prepend-icon="mdi-check"
              @click="dialogConfirm = true"
            >
              Submit
            </v-btn>
          </template>
          <span>Submit For Approval</span>
        </v-tooltip>

        <v-tooltip v-if="$hasRole(['Superuser'])" top style="margin-top: 4px">
          <template v-slot:activator="{ props }">
            <v-btn
              v-if="$hasRole(['Superuser'])"
              size="small"
              v-bind="props"
              :disabled="$hasRole(['Superuser']) ? false : isDisable"
              variant="flat"
              color="primary"
              class="white--text ml-1"
              prepend-icon="mdi-check"
              @click="dialogConfirm = true"
            >
              Re Submit
            </v-btn>
          </template>
          <span>Submit For Approval</span>
        </v-tooltip>

        <v-tooltip top style="margin-top: 4px; margin-left: 4px">
          <template v-slot:activator="{ props }">
            <v-btn
              v-bind="props"
              :loading="submitLoad3"
              elevation="0"
              :disabled="isDisable"
              color="error darken-1"
              class="white--text ml-1"
              variant="plain"
              size="small"
              icon="mdi-delete"
              @click="deleteAll()"
            >
            </v-btn>
          </template>
          <span>Delete All</span>
        </v-tooltip>
      </v-card-actions>
    </v-card>

    <v-dialog v-model="dialogConfirm" persistent max-width="290">
      <v-card>
        <v-card-title class="headline"> Submit for approval? </v-card-title>
        <v-card-text>
          Are you sure you want to submit for approval?
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn
            color="error darken-1"
            text
            density="compact"
            variant="flat"
            @click="dialogConfirm = false"
          >
            No
          </v-btn>
          <v-btn
            :key="buttonKey"
            color="primary darken-1"
            dark
            prepend-icon="mdi-send"
            density="compact"
            variant="flat"
            :loading="submitLoad3"
            @click.once="save('all', true)"
          >
            Yes, Submit
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <LazySnackbar ref="snackbar"></LazySnackbar>

    <LazyResvItemMasterData
      ref="itemsMaster"
      :form="form"
      @selectItems="selectItems"
    ></LazyResvItemMasterData>

    <LazyResvUomMasterData
      ref="uomMaster"
      :form="form"
      @selectUom="selectUom"
    ></LazyResvUomMasterData>

    <LazyResvOrderIdMasterData
      ref="orderIdMaster"
      :form="form"
      @selectOrderId="selectOrderId"
    ></LazyResvOrderIdMasterData>

    <LazyResvAssetMasterData
      ref="assetsMaster"
      :form="form"
      @selectAssets="selectAssets"
    ></LazyResvAssetMasterData>

    <LazyResvLastResv ref="lastResv" :form="form"></LazyResvLastResv>

    <LazyResvDialogDelete
      ref="dialogDelete"
      :url="urlDelete"
      @showMessage="showMessage"
    ></LazyResvDialogDelete>

    <LazySpinnerLoading
      v-if="dialogLoading"
      ref="spinnerLoadingImport"
    ></LazySpinnerLoading>

    <LazyAttachment
      ref="attachment"
      @eventCountAttachment="eventCountAttachment"
    ></LazyAttachment>

    <LazyResvInboundDialog
      ref="inboundDialog"
    ></LazyResvInboundDialog>

    <LazyResvShipmentDialog
      ref="shipmentDialog"
    ></LazyResvShipmentDialog>
  </v-dialog>
</template>

<script lang="ts">
import print from "print-js";

export default {
  name: "ReserVationForm",
  data() {
    return {
      dataTableOptions: {},
      itemsPerPage: 20,
      buttonKey: 1,
      buttonKey2: 2,
      buttonKey3: 3,
      count_attachment: 0,
      menu1: "",
      menu: "",
      dialog: false,
      date: null,
      urlDelete: null,
      dialogLoading: false,
      dialogConfirm: false,
      submitLoad: false,
      submitLoad2: false,
      submitLoad3: false,
      loading: false,
      loadingPrint: false,
      loadingChangeCompany: false,
      isReadOnly: false,
      loadingPrint2: false,
      message: false,
      success: false,
      itemDivision: [],
      itemUserNIK: [],
      itemCompany: [],
      itemWhTo: [],
      itemUserWhs: [],
      itemDataUsage: [],
      itemCostCenter: [],
      userDivision: [],
      breadcrumb: [],
      itemReqType: ["Normal", "Restock", "Project"],
      itemUsageFor: ["Internal", "External"],
      itemCategoryType: ["General", "Fuel", "APD"],
      itemEmployeeType: ["Normal", "New Employee", "Inventaris"],
      itemCostType: ["Cost", "Inventory"],
      itemDocumentType: [],
      ItemType: ["Ready Stock", "Non Ready Stock", "Asset", "Service"],
      itemCustomer: [],
      itemOrderId: [],
      itemVehicleNo: [],
      isDisable: false,
      isApprovalDisable: false,
      isDataSet: false,
      Division: {},
      U_NIK: {},
      title: "Reservation Request",
      status: null,
      docId: null,
      dataSchema: [],
      form: {
        Company: null,
        CompanyName: null,
        DocNum: null,
        UserName: null,
        U_NIK: this.$auth.user.username,
        RequesterName: this.$auth.user.name,
        Division: this.$auth.user.department,
        Department: null,
        DocDate: null,
        RequiredDate: null,
        WhsCode: null,
        RequestType: null,
        ApprovalStatus: "-",
        U_DocEntry: null,
        LastApproved: null,
        Memo: null,
        Token: null,
        WhTo: null,
        UrgentReason: null,
        ItemType: null,
        ItemTypeCheck: null,
        Is_Urgent: "No",
        CategoryType: "General",
        EmployeeType: "Normal",
        UsageFor: "External",
        VehicleNo: null,
        Mileage: null,
        Customer: null,
        Replacement: null,
        CostType: null,
        CostCenter: null,
        DocumentType: "Item",
      },
      defaultForm: {
        Company: null,
        CompanyName: null,
        DocNum: null,
        UserName: null,
        U_NIK: this.$auth.user.username,
        RequesterName: this.$auth.user.name,
        Division: this.$auth.user.department,
        Department: null,
        DocDate: null,
        RequiredDate: null,
        WhsCode: null,
        RequestType: null,
        ApprovalStatus: "-",
        U_DocEntry: null,
        LastApproved: null,
        Memo: null,
        Token: null,
        WhTo: null,
        UrgentReason: null,
        ItemType: null,
        ItemTypeCheck: null,
        Is_Urgent: "No",
        CategoryType: "General",
        UsageFor: "External",
        EmployeeType: "Normal",
        VehicleNo: null,
        Mileage: null,
        Customer: null,
        Replacement: null,
        CostType: null,
        CostCenter: null,
        DocumentType: "Item",
      },
    };
  },
  head() {
    return {
      title: this.title,
    };
  },
  computed: {
    btnTitle() {
      return this.status === "add" ? "Save As Draft" : "Save As Draft";
    },
  },
  created() {
    // this.getUserRelationship()
    // this.getData()
    // this.getDivision()
  },
  mounted() {
    // this.getData()
    this.getDivision();
    this.getUserRelationship();
    // this.changeCompany();
    this.getDataUsage(this.$auth.user.department, this.$auth.user.location);
    this.getDataCostCenter(this.$auth.user.department, this.$auth.user.location);
    this.changeItemType();
    if (this.$auth.user.is_admin_subwh === "N") {
      this.itemReqType = ["Normal", "Project", "Project"];
      this.form.RequestType = "Normal";
    } else {
      // this.itemReqType = ['Normal', 'Restock', 'Sales']
      this.itemReqType = ["Normal", "Restock", "Project"];
      this.form.RequestType = null;
    }

    if (
      this.$auth.user.location === "BDM MOROWALI" &&
      this.form.WhsCode === "BG02"
    ) {
      this.ItemType = ["Ready Stock", "Asset"];
    }
  },
  methods: {
    changeItemType() {
      if (this.$hasRole(["Admin E-RESERVATION Jakarta"])) {
        if (this.$hasRole(["Admin E-RESERVATION Jakarta Item Access"])) {
          this.ItemType = [
            "Ready Stock",
            "Non Ready Stock",
            "Asset",
            "Service",
          ];
        } else {
          this.ItemType = ["Asset", "Service"];
        }
      } else {
        this.ItemType = ["Ready Stock", "Non Ready Stock", "Asset", "Service"];
      }
    },

    updateOptions(params: any) {
      this.dataTableOptions = params;
      this.getData();
    },

    printDocument(type = "all") {
      const item = this.form;
      // if (type === 'all') {
      //   this.loadingPrint2 = true
      // } else {
      //   this.loadingPrint = true
      // }
      this.dialogLoading = true;
      this.$fetchData(`/api/reservation/print`, {
        params: {
          form: item,
          type,
        },
        // responseType: 'arraybuffer',
      })
        .then((res: any) => {
          print({ printable: res.data.base64, type: "pdf", base64: true });
          // window.open(res.message, '_blank')
          this.dialogLoading = false;
        })
        // eslint-disable-next-line node/handle-callback-err
        .catch((err: any) => {
          this.dialogLoading = false;
          console.log(err);
          this.$swal.fire({
            icon: "error",
            title: "Error",
            text: err.data.message,
          });
        });
    },

    checkCostType() {
      if (this.$auth.user.location.includes("JAKARTA")) {
        if (this.$hasRole(["Admin E-RESERVATION Jakarta Item Access"])) {
          return true;
        } else {
          return false;
        }
      } else {
        return true;
      }
    },
    getDataUsage(division, workLocation) {
      this.$fetchData("/api/resv-sap-usage", {
        params: {
          division,
          workLocation,
        },
      }).then((res: any) => {
        this.itemDataUsage = res.data.pluck;
      });
    },
    getDataCostCenter(division, workLocation) {
      this.$fetchData("/api/resv-cost-center", {
        params: {
          division,
          workLocation,
        },
      }).then((res: any) => {
        this.itemCostCenter = res.data.pluck;
      });
    },
    openForm(title, status, id) {
      this.status = status;
      this.docId = id;
      this.dialog = true;
      this.form = Object.assign({}, this.defaultForm);
      this.getData(id, status);
      // this.getDivision()
      // this.getUserRelationship()
      this.title = title;
    },
    enableClickOnce() {
      this.buttonKey++;
    },

    statusColor(status) {
      switch (status) {
        case "-":
        case "planned":
          return "blue darken-3";
        case "partial":
        case "released":
        case "Waiting":
          return "warning";
        case "paid":
          return "primary";
        case "Approve":
          return "primary";
        case "overdue":
          return "error";
        case "Rejected":
        case "Reject":
        case "cancel":
          return "error";
      }
    },

    close() {
      this.dialog = false;
      this.isDisable = false;
      this.$router.push({ path: "/reservation/request" });
      this.title = "Reservation Request";
      this.form = Object.assign({}, this.defaultForm);
    },
    changeDate() {
      // eslint-disable-next-line no-extend-native
      Date.prototype.addDays = function (days) {
        const date = new Date(this.valueOf());
        date.setDate(date.getDate() + days);
        return date;
      };
      // Add 7 Days
      const date = new Date(this.form.DocDate);
      this.form.RequiredDate = this.formatDate(date.addDays(7));
    },
    formatDate(date) {
      const d = new Date(date);
      let month = "" + (d.getMonth() + 1);
      let day = "" + d.getDate();
      const year = d.getFullYear();
      if (month.length < 2) month = "0" + month;
      if (day.length < 2) day = "0" + day;
      return [year, month, day].join("-");
    },

    getUserRelationship() {
      localStorage.setItem("request_type", JSON.stringify(["NPB", "SPB"]));
      this.$fetchData(`/api/master/users/relationship`, {
        params: {
          form: this.form,
        },
      }).then((res: any) => {
        this.itemCompany = res.items;
        this.itemUserNIK = res.userNik;
        // this.itemUserWhs = res.itemUserWhs;
        this.userDivision = res.userDivision;
        this.itemDocumentType = res.itemDocumentType;
        this.itemOrderId = res.ioOrder;
      });
    },

    changeCompany() {
      console.log(this.form.CompanyName);
      this.loadingChangeCompany = true;
      this.$fetchData("/api/master/change-company", {
        params: {
          company: this.form.CompanyName,
        },
      })
        .then((res: any) => {
          this.itemUserWhs = res.itemUserWhs;
        })
        .finally(() => {
          this.loadingChangeCompany = false;
        });
    },

    getDivision() {
      this.$fetchData(`/api/master/division`).then((res: any) => {
        this.itemDivision = res.all_division;
        this.itemCustomer = res.customer;
        this.itemVehicleNo = res.vehicle;
        this.$auth.$storage.setState("vehicle", this.itemVehicleNo);
      });
    },
    getWhsTo() {
      this.$fetchData(`/api/master/users/whsto`, {
        params: {
          form: this.form,
        },
      }).then((res: any) => {
        this.itemWhTo = res.itemWhTo;
      });
    },
    openDeleteRow(data) {
      this.urlDelete = data.url;
      this.$refs.dialogDelete.openDialog(data.url, data.row);
    },
    showMessage(message) {
      this.$refs.snackbar.display(10000, message);
      this.success = message;
      this.getData();
      setTimeout(() => (this.success = false), 8000);
    },
    openDialog(row) {
      if (this.form.CompanyName && this.form.WhsCode) {
        this.$refs.itemsMaster.openDialog(row, this.form);
      } else {
        this.$refs.snackbar.display(10000, "Company and WhsCode Cannot Empty!");
      }
    },
    openDialogAsset(row) {
      this.$refs.assetsMaster.openDialog(row);
    },

    openDialogUom(row) {
      this.$refs.uomMaster.openDialog(row);
    },
    openDialogOrderId(row) {
      this.$refs.orderIdMaster.openDialog(row);
    },
    openDialogLastResv(data) {
      if (this.form.CompanyName && this.form.WhsCode && this.form.DocDate) {
        // let itemCode = this.$refs.childDetails.getDataAtRowPropDetails(row, 'ItemCode')
        // let reqDate = this.$refs.childDetails.getDataAtRowPropDetails(row, 'ReqDate')
        // let whsCode = this.$refs.childDetails.getDataAtRowPropDetails(row, 'WhsCode')
        this.$refs.lastResv.openDialog(
          data.row,
          data.itemCode,
          data.reqDate,
          data.whsCode,
          data.itemName,
        );
      } else {
        this.$refs.snackbar.display(10000, "Company and WhsCode Cannot Empty!");
      }
    },
    closeDialog() {
      this.$refs.itemsMaster.closeDialog();
    },
    selectItems(data) {
      this.$refs.childDetails.insertDataToDetails(data.row, data.selected);
    },

    changeTable() {
      this.$refs.childDetails.changeTable(
        this.form.DocumentType,
        this.form.ItemType,
        this.dataSchema,
      );
    },

    selectAssets(data) {
      this.$refs.childDetails.insertDataAssetToDetails(data.row, data.selected);
    },

    selectOrderId(data) {
      this.$refs.childDetails.insertDataOrderToDetails(data.row, data.selected);
    },

    selectUom(data) {
      if (this.form.DocumentType == 'Service') {
        this.$refs.childDetails.insertDataUomToDetails(data.row, data.selected);
      }
    },
    changeWhs() {
      if (
        this.$auth.user.location === "BDM MOROWALI" &&
        this.form.WhsCode === "BG02"
      ) {
        this.ItemType = ["Ready Stock", "Asset"];
      } else {
        this.changeItemType();
      }
      this.$refs.childDetails.changeWhsDetails();
    },

    changeDivision() {
      const nik = this.form.U_NIK;
      // eslint-disable-next-line no-unused-vars
      let selectedDivision = "";
      const vm = this;
      this.userDivision.forEach(function (item) {
        if (item.U_NIK === nik) {
          selectedDivision = item.Division;
          vm.form.RequesterName = item.U_UserName;
          vm.form.Division = item.Division;

          vm.getDataUsage(vm.form.Division, item.WorkLocation);
          vm.getDataCostCenter(vm.form.Division, item.WorkLocation);
        }
      });
    },

    fillCheckbox() {
      this.$refs.childDetails.fillCheckboxDetails();
      if (this.form.RequestType === "Restock") {
        this.ItemType = ["Ready Stock"];
      } else if (this.form.RequestType === "Project") {
        this.ItemType = ["Non Ready Stock"];
      } else if (this.form.RequestType === "Sales") {
        this.ItemType = ["Ready Stock", "Non Ready Stock"];
      } else {
        this.ItemType = ["Ready Stock", "Non Ready Stock", "Asset", "Service"];
      }
    },

    changeCategoryType() {
      this.$refs.childDetails.fillCheckboxDetails2();
      if (this.form.CategoryType === "Triwulan") {
        this.ItemType = ["Ready Stock"];
      } else {
        this.ItemType = ["Ready Stock", "Non Ready Stock", "Asset", "Service"];
      }
    },

    changeUsageFor() {
      if (this.form.UsageFor === "Internal") {
        this.form.Customer = null;
      } else if (this.form.UsageFor === "External") {
        this.form.Customer = "SULAWESI MINING INVESTMENT,PT (IDR)";
      }
    },

    refreshData() {
      this.getData();
    },

    eventCountAttachment(data) {
      if (data.type === "reservation") {
        this.$refs.childDetails.setCountAttachment(data.total, data.row);
      } else {
        this.count_attachment = data.total;
      }
    },
    openAttachmentDetails(row) {
      this.$refs.attachment.openAttachment(
        row.lineEntry,
        "reservation",
        row.row,
      );
    },
    openAttachment() {
      this.$refs.attachment.openAttachment(this.docId, "reservation_header", 0);
    },
    openDialogInbound(data) {
      const rowData = this.$refs.childDetails.getSourceDataAtRow(data.row);
      this.$refs.inboundDialog.openDialog(rowData, data.inboundNo);
    },
    openDialogShipment(data) {
      const rowData = this.$refs.childDetails.getSourceDataAtRow(data.row);
      this.$refs.shipmentDialog.openDialog(rowData, data.shipmentNo);
    },
    addLine() {
      const vm = this;
      if (
        this.form.CompanyName &&
        this.form.WhsCode &&
        this.form.RequestType &&
        this.form.DocDate &&
        this.form.ItemType
      ) {
        vm.$refs.childDetails.addLine();
        if (this.$refs.childDetails.countDetailsRows() > 1) {
          this.isReadOnly = true;
        }
      } else {
        this.$refs.snackbar.display(
          10000,
          "Company, Request Type, Request Date, Item Type and WhsCode Cannot Empty!",
        );
      }
    },
    checkCount() {
      if (this.$refs.childDetails.countDetailsRows() > 1) {
        this.isReadOnly = true;
      }
    },
    deleteAll() {
      const id = this.docId;
      const status = this.status;
      if (status === "update") {
        const url = "/api/reservation/delete-all/" + id;
        this.$refs.dialogDelete.openDialog(url);
      }
    },
    save(type = "all", approval = false) {
      const vm = this;
      const form = this.form;
      const clearData = {};
      const status = this.status;
      vm.submitLoad = true;
      const details = vm.$refs.childDetails.getAddData();
      details.forEach(function (item, key) {
        if (!vm.$refs.childDetails.checkIfEmptyRow(key)) clearData[key] = item;
      });
      const data = {
        form,
        details: clearData,
        type,
        status,
        Division: this.Division,
        U_NIK: this.U_NIK,
        approval,
      };
      if (status === "add") {
        this.store("post", "/api/reservation/master", data, "insert", type);
        vm.submitLoad = false;
      } else if (status === "update") {
        this.store(
          "put",
          "/api/reservation/master/" + this.form.U_DocEntry,
          data,
          "update",
          type,
        );
        vm.submitLoad = false;
      }
      vm.submitLoad = false;
    },

    store(method, url, data, type, column = "all") {
      const vm = this;
      vm.submitLoad = true;
      vm.isDisable = true;
      this.dialogLoading = true;
      this.$fetchData(url, { method, body: data })
        .then((res: any) => {
          this.dialogConfirm = false;
          this.$swal.fire({
            icon: "success",
            title: "Success!",
            text: res.message,
          });
          this.success = res.message;
          vm.$refs.snackbar.display(1800, res.message);
          vm.submitLoad = false;
          vm.isDisable = false;
          setTimeout(() => (this.success = false), 8000);
          this.dialogLoading = false;
          this.enableClickOnce();
          setTimeout(() => {
            vm.getData(res.data.U_DocEntry, "update");
            this.status = "update";
            vm.$emit("getData");
          }, 500);
          this.$router.push({
            path: "/reservation/request",
            query: { id: res.data.U_DocEntry, status: "update" },
          });
        })
        // eslint-disable-next-line node/handle-callback-err
        .catch((err: any) => {
          this.dialogLoading = false;
          this.enableClickOnce();
          vm.submitLoad = false;
          vm.isDisable = false;
          this.$swal.fire({
            icon: "error",
            title: "Error",
            text: err.data.message,
          });
        });
    },

    getData(docEntry = null, statusUrl = null) {
      // eslint-disable-next-line no-unused-vars
      const vm = this;
      const id = docEntry || this.docId;
      const status = statusUrl || this.status;
      const entry =
        this.$route.query.entry !== undefined ? this.$route.query.entry : 0;
      this.isApprovalDisable = this.status === "add";

      // console.log(this.$gates.hasRole('Admin E-RESERVATION Jakarta'))
      if (this.$hasRole(["Admin E-RESERVATION Jakarta"])) {
        if (this.$hasRole(["Admin E-RESERVATION Jakarta Item Access"])) {
          this.itemReqType = ["Normal", "Restock"];
          this.itemCategoryType = ["General", "Fuel"];
        } else {
          this.itemReqType = ["Normal"];
          this.itemCategoryType = ["General"];
        }
      } else if (this.$hasRole(["Superuser"])) {
        this.itemCategoryType = ["General", "Fuel", "APD", "Triwulan"];
      } else {
        this.itemReqType = ["Normal", "Restock", "Project"];
        this.itemCategoryType = ["General", "Fuel", "APD"];
      }

      if (this.$auth.user.location === "BDM MOROWALI") {
        this.itemCategoryType = ["General", "APD", "Triwulan"];
      }

      if (this.$hasRole(["Admin E-RESERVATION Jakarta"])) {
        this.form.DocumentType = "Service";
        this.form.ItemType = "Service";
        this.form.RequestType = "Normal";
        this.form.CategoryType = "General";
      }
      // this.form.CompanyName = "IMIP_LIVE";

      if (status === "update") {
        this.dialogLoading = true;
        this.$fetchData(`/api/reservation/master/${id}`, {
          params: {
            entry,
          },
        })
          .then((res: any) => {
            this.count_attachment = res.count_attachment;
            // Fill Header
            if (res.header !== undefined) {
              this.form = Object.assign({}, res.header);
              this.Division = Object.assign({}, res.division);
              this.U_NIK = Object.assign({}, res.user_nik);
              if (
                this.form.ApprovalStatus !== "-" &&
                this.form.ApprovalStatus !== "N"
              ) {
                this.isDisable = true;
                this.isApprovalDisable = true;
              }
              if (
                parseInt(res.header.CreatedBy) !==
                parseInt(this.$auth.user.username)
              ) {
                this.isDisable = true;
                this.isApprovalDisable = true;
              }
              if (res.header.AppStatus === "Reject") {
                this.isDisable = true;
                this.isApprovalDisable = true;
              }
            }

            this.getDataUsage(this.form.Division, this.form.WorkLocation);
            this.getDataCostCenter(this.form.Division, this.form.WorkLocation);
            this.title = "Edit Reservation #" + res.header.DocNum;
            this.docId = res.header.U_DocEntry;
            this.enableClickOnce();
            this.dataSchema = res.dataSchema;
            // this.form.CompanyName = res.userCompany;
            this.changeCompany();
            this.$auth.$storage.setState("apdNotes", res.apdNotes);
            // Fill details
            if (res.rows !== undefined) {
              setTimeout(() => {
                this.$refs.childDetails.setIoOrder(this.itemOrderId);
                this.$refs.childDetails.setDataToDetails(
                  res.rows,
                  res.dataSchema,
                );
                this.changeTable(this.form.DocumentType, this.form.ItemType);
              }, 200);
            }
            this.dialogLoading = false;
            this.$refs.snackbar.display(6000, "Data loaded...");
          })
          .catch((err: any) => {
            this.dialogLoading = false;
            this.$swal.fire({
              icon: "error",
              title: "Error",
              text: err.data.message,
            });
          });
      } else {
        this.dialogLoading = true;
        this.$fetchData(`/api/reservation/fetch-docnum`)
          .then((res: any) => {
            this.enableClickOnce();
            this.form.DocNum = res.DocNum;
            this.form.Token = res.token;
            this.form.CompanyName = res.userCompany;
            this.$auth.$storage.setState("department", res.department);
            this.$auth.$storage.setState("apdNotes", res.apdNotes);
            if (this.$hasRole(["HRD Morowali"])) {
              this.form.EmployeeType = "New Employee";
            }

            this.changeCompany();

            setTimeout(() => {
              this.$refs.childDetails.setIoOrder(this.itemOrderId);
              this.dataSchema = res.dataSchema;
              this.$refs.childDetails.setDataToDetails([], res.dataSchema);
              this.changeTable(this.form.DocumentType, this.form.ItemType);
            }, 500);
          })
          .finally(() => {
            this.dialogLoading = false;
          });
      }
    },
  },
};
</script>
