{"name": "eresv", "private": true, "type": "module", "scripts": {"build": "NODE_OPTIONS='--max-old-space-size=4096' nuxt build", "build:portal": "cp .env.portal .env &&  nuxt build", "dev": "cp .env.locals .env && nuxt dev", "dev-public": "cp .env.dev.public .env && nuxt dev", "generate:portal": "cp .env.portal .env && nuxt generate", "generate": "cp .env.productions .env &&  cp middleware/portal.ts middleware/portal.global.ts  && nuxt generate &&  cp middleware/portal.global.ts middleware/portal.ts && rm -rf ../../../../nuxt-generated-file/eresv-generate/_nuxt/  && cp -rf .output/public/* ../../../../nuxt-generated-file/eresv-generate/ ", "generate:live": "cp .env.productions .env && mv middleware/portal.ts middleware/portal.global.ts && rm -rf .output/* && nuxt generate && mv middleware/portal.global.ts middleware/portal.ts && rm -rf ../../../../nuxt-generated-file/eresv-generate/_nuxt/  && cp -rf .output/public/* ../../../../nuxt-generated-file/eresv-generate/ ", "generate:live-public": "cp .env.production.public .env && mv middleware/portal.ts middleware/portal.global.ts && rm -rf .output/* && nuxt generate && mv middleware/portal.global.ts middleware/portal.ts && rm -rf ../../../../nuxt-generated-file/eresv-generate/_nuxt/  && cp -rf .output/public/* ../../../../nuxt-generated-file/eresv-generate/ ", "generate:dev": "cp .env.dev .env && rm -rf .output/* && nuxt generate && rm -rf ../../../../nuxt-generated-file/eresv-generate/_nuxt/  && cp -rf .output/public/* ../../../../nuxt-generated-file/eresv-generate/ ", "generate:dev-public": "cp .env.dev.public .env && rm -rf .output/* && nuxt generate && rm -rf ../../../../nuxt-generated-file/eresv-generate/_nuxt/  && cp -rf .output/public/* ../../../../nuxt-generated-file/eresv-generate/ ", "serve": "node .output/server/index.mjs", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "dependencies": {"@ckeditor/ckeditor5-basic-styles": "^40.0.0", "@ckeditor/ckeditor5-build-classic": "^40.0.0", "@ckeditor/ckeditor5-ckfinder": "^41.2.1", "@ckeditor/ckeditor5-editor-classic": "^40.0.0", "@ckeditor/ckeditor5-essentials": "^40.0.0", "@ckeditor/ckeditor5-link": "^40.0.0", "@ckeditor/ckeditor5-paragraph": "^40.0.0", "@ckeditor/ckeditor5-theme-lark": "^40.0.0", "@ckeditor/ckeditor5-vue": "^5.1.0", "@ckeditor/vite-plugin-ckeditor5": "^0.1.3", "@handsontable/vue3": "^14.1.0", "@sweetalert2/theme-material-ui": "^5.0.16", "handsontable": "^14.1.0", "nuxt": "^3.10.3", "pdfjs-dist": "^4.6.82", "postcss": "^8.3.3", "print-js": "^1.6.0", "vite": "^5.3.5", "vue": "^3.4.21", "vue-masonry": "^0.16.0", "vue-router": "^4.3.0", "vue2-dropzone-vue3": "^1.1.0", "vue3-perfect-scrollbar": "^1.6.1", "vuetify": "^3.5.1"}, "devDependencies": {"@vitejs/plugin-vue": "^5.1.2"}}