<template>
  <v-row>
    <v-col cols="12" sm="12">
      <v-card>
        <v-toolbar
          flat
          color="grey lighten-2"
          density="compact"
          style="height: 32px"
          class="toolbar-content"
        >
          <v-divider class="mx-2" inset vertical></v-divider>
          <!--<breadcrumbs/>-->

          <v-breadcrumbs
            :items="breadcrumb"
            divider="/"
            class="hidden-xs-only"
          ></v-breadcrumbs>

          <v-spacer class="hidden-xs-only"></v-spacer>
          <v-btn
            v-if="
              form.ApprovalStatus === 'Y' && form.ItemType === 'Non Ready Stock'
            "
            size="small"
            density="compact"
            elevation="4"
            color="primary darken-1"
            class="white--text"
            :loading="loadingPrint2"
            @click="printDocument('all')"
          >
            <v-icon left> mdi-printer </v-icon>
            Print All
          </v-btn>

          <v-btn
            v-if="form.ApprovalStatus === 'Y'"
            size="small"
            density="compact"
            elevation="4"
            color="primary darken-1"
            class="white--text ml-3"
            :loading="loadingPrint"
            @click="printDocument('partial')"
          >
            <v-icon left> mdi-printer </v-icon>
            Print
          </v-btn>

          <v-btn :loading="loading" icon @click="refreshData">
            <v-icon>mdi-refresh</v-icon>
          </v-btn>
        </v-toolbar>

        <v-form>
          <v-row wrap>
            <v-col cols="12" v-if="message" sm="12">
              <div class="red darken-2 text-xs-center">
                <span class="white--text">{{ message }}</span>
              </div>
            </v-col>

            <v-col cols="12" v-if="success" sm="12">
              <div class="primary darken-2 text-xs-center">
                <span class="white--text">{{ success }}</span>
              </div>
            </v-col>

            <v-col cols="12" md="12" class="d-flex">
              <v-row wrap class="pa-2">
                <v-col cols="12" md="6" sm="12" class="pa-0">
                  <v-row no-gutters class="pt-1">
                    <v-col cols="4" sm="3" md="2" class="pt-1">
                      <label>Company</label>
                    </v-col>
                    <v-col cols="8" sm="9" md="4" lg="4" class="pr-md-2 pt-1">
                      <v-select
                        v-model="form.CompanyName"
                        :items="itemCompany"
                        placeholder="Company"
                        :readonly="isReadOnly"
                        variant="outlined"
                        density="compact"
                        hide-details="auto"
                      ></v-select>
                    </v-col>

                    <v-col cols="4" sm="3" md="2" class="pt-1">
                      <label>Warehouse</label>
                    </v-col>
                    <v-col cols="8" sm="9" md="4" lg="4" class="pt-1">
                      <v-select
                        v-model="form.WhsCode"
                        :items="itemUserWhs"
                        placeholder="Warehouse"
                        :readonly="isReadOnly"
                        variant="outlined"
                        density="compact"
                        hide-details="auto"
                        @update:modelValue="changeWhs()"
                      ></v-select>
                    </v-col>
                  </v-row>

                  <v-row no-gutters class="pt-1">
                    <v-col cols="4" sm="3" md="2" class="pt-1">
                      <label>Requester</label>
                    </v-col>
                    <v-col cols="8" sm="9" md="4" lg="4" class="pr-md-2 pt-1">
                      <v-autocomplete
                        v-model="form.U_NIK"
                        :items="itemUserNIK"
                        filled
                        placeholder="Requester"
                        item-title="U_UserName"
                        item-value="U_NIK"
                        :readonly="isReadOnly"
                        density="compact"
                        variant="outlined"
                        hide-details="auto"
                        @update:modelValue="changeDivision()"
                      >
                        <template #item="data">
                          <template v-if="typeof data.item !== 'object'">
                            <div>{{ data.item }}</div>
                          </template>
                          <template v-else>
                            <div>
                              <v-list-item-title
                                v-text="data.item.U_NIK"
                              ></v-list-item-title>
                              <v-list-item-subtitle
                                v-text="data.item.U_UserName"
                              ></v-list-item-subtitle>
                            </div>
                          </template>
                        </template>
                      </v-autocomplete>
                    </v-col>

                    <v-col cols="4" sm="3" md="2" class="pt-1">
                      <label>Division</label>
                    </v-col>
                    <v-col cols="8" sm="9" md="4" lg="4" class="pt-1">
                      <v-autocomplete
                        v-model="form.Division"
                        :items="itemDivision"
                        filled
                        placeholder="Division"
                        :readonly="isReadOnly"
                        density="compact"
                        variant="outlined"
                        hide-details="auto"
                      ></v-autocomplete>
                      <!--  <v-text-field
                        v-model='form.Division'
                        readonly
                        placeholder='Division'
                        variant="outlined" density="compact"
                        hide-details='auto'
                      ></v-text-field> -->
                    </v-col>
                  </v-row>

                  <v-row no-gutters class="pt-1">
                    <v-col cols="4" sm="3" md="2" class="pt-1">
                      <label>Request Type</label>
                    </v-col>
                    <v-col cols="8" sm="9" md="4" lg="4" class="pr-md-2 pt-1">
                      <v-select
                        v-model="form.RequestType"
                        :items="itemReqType"
                        :readonly="isReadOnly"
                        placeholder="Request Type"
                        variant="outlined"
                        density="compact"
                        hide-details="auto"
                        @update:modelValue="fillCheckbox()"
                      ></v-select>
                    </v-col>

                    <v-col
                      v-if="form.RequestType === 'For Restock SubWH'"
                      cols="4"
                      sm="3"
                      md="2"
                      class="pt-1"
                    >
                      <label>Wh Transfer To</label>
                    </v-col>
                    <v-col
                      v-if="form.RequestType === 'For Restock SubWH'"
                      cols="8"
                      sm="9"
                      md="4"
                      lg="4"
                      class="pt-1"
                    >
                      <v-select
                        v-model="form.WhTo"
                        :items="itemUserWhs"
                        :readonly="isReadOnly"
                        placeholder="Wh Transfer To"
                        variant="outlined"
                        density="compact"
                        hide-details="auto"
                      ></v-select>
                    </v-col>
                  </v-row>

                  <v-row v-if="form.ItemType === 'Non Ready Stock'" no-gutters>
                    <v-col cols="4" sm="3" md="2">
                      <label>Is Urgent?</label>
                    </v-col>
                    <v-col cols="8">
                      <v-checkbox
                        v-model="form.Is_Urgent"
                        value="Yes"
                        density="compact"
                        hide-details="auto"
                      ></v-checkbox>
                    </v-col>
                  </v-row>

                  <v-row
                    v-if="form.Is_Urgent === 'Yes'"
                    no-gutters
                    class="pt-1"
                  >
                    <v-col cols="4" sm="3" md="2">
                      <label>Urgent Reason</label>
                    </v-col>
                    <v-col cols="8" sm="9" md="10" lg="10" xl="10">
                      <v-text-field
                        v-model="form.UrgentReason"
                        placeholder="Urgent Reason"
                        variant="outlined"
                        density="compact"
                        hide-details="auto"
                      ></v-text-field>
                    </v-col>
                  </v-row>
                </v-col>

                <v-col
                  cols="12"
                  md="1"
                  sm="12"
                  class="hidden-sm-and-down"
                ></v-col>

                <v-col cols="12" md="5" sm="12" class="pa-0">
                  <v-row no-gutters>
                    <v-col cols="4" sm="3" md="2" class="pt-1">
                      <label>Item Type</label>
                    </v-col>
                    <v-col cols="8" sm="9" md="4" lg="4" class="pr-md-2 pt-1">
                      <v-select
                        v-model="form.ItemType"
                        :items="ItemType"
                        placeholder="Item Type"
                        variant="outlined"
                        density="compact"
                        hide-details="auto"
                        :readonly="
                          $route.query.status === 'update' || isReadOnly
                        "
                      ></v-select>
                    </v-col>

                    <v-col cols="4" sm="3" md="2" class="pt-1">
                      <label>No</label>
                    </v-col>
                    <v-col cols="8" sm="9" md="4" lg="4" class="pt-1">
                      <v-text-field
                        v-model="form.DocNum"
                        readonly
                        placeholder="NO"
                        variant="outlined"
                        density="compact"
                        hide-details="auto"
                      ></v-text-field>
                    </v-col>
                  </v-row>

                  <v-row no-gutters class="pt-1">
                    <v-col cols="4" sm="3" md="2" class="pt-1">
                      <label>Request Date</label>
                    </v-col>
                    <v-col cols="8" sm="9" md="4" lg="4" class="pr-md-2 pt-1">
                      <v-menu
                        ref="menu"
                        v-model="menu"
                        :close-on-content-click="false"
                        transition="scale-transition"
                        offset-y
                        min-width="290px"
                      >
                        <template v-slot:activator="{ props }">
                          <v-text-field
                            v-model="form.DocDate"
                            placeholder="Request Date"
                            prepend-icon="mdi-calendar"
                            readonly
                            persistent-hint
                            variant="outlined"
                            density="compact"
                            hide-details="auto"
                            v-bind="props"
                          ></v-text-field>
                        </template>

                        <v-date-picker
                          v-model="form.DocDate"
                          no-title
                          @update:modelValue="changeDate"
                          @input="menu = false"
                        >
                        </v-date-picker>
                      </v-menu>
                    </v-col>

                    <v-col cols="4" sm="3" md="2" class="pt-1">
                      <label>Required Date</label>
                    </v-col>
                    <v-col cols="8" sm="9" md="4" lg="4" class="pt-1">
                      <v-menu
                        ref="menu1"
                        v-model="menu1"
                        :close-on-content-click="false"
                        transition="scale-transition"
                        offset-y
                        min-width="290px"
                      >
                        <template v-slot:activator="{ props }">
                          <v-text-field
                            v-model="form.RequiredDate"
                            placeholder="Required Date"
                            prepend-icon="mdi-calendar"
                            readonly
                            persistent-hint
                            variant="outlined"
                            density="compact"
                            hide-details="auto"
                            v-bind="props"
                          ></v-text-field>
                        </template>

                        <v-date-picker
                          v-model="form.RequiredDate"
                          no-title
                          @input="menu1 = false"
                        >
                        </v-date-picker>
                      </v-menu>
                    </v-col>
                  </v-row>

                  <v-row no-gutters>
                    <v-col cols="4" sm="3" md="2" class="pt-1">
                      <label>Approval Status</label>
                    </v-col>

                    <v-col cols="8" sm="9" md="4" lg="4" class="pr-2 pt-1">
                      <span>: {{ form.AppStatus }}</span>
                    </v-col>

                    <v-col cols="4" sm="3" md="2" class="pt-1">
                      <label>Last Approved By</label>
                    </v-col>

                    <v-col cols="8" sm="9" md="4" lg="4" class="pt-1">
                      <span>: {{ form.LastApproved }}</span>
                    </v-col>
                  </v-row>
                </v-col>
              </v-row>
            </v-col>

            <v-col cols="12" md="12" class="d-flex">
              <v-row wrap></v-row>
            </v-col>

            <div class="scroll-container">
              <!--Details -->
              <LazyResvReservationDetails
                ref="childDetails"
                :form="form"
                :is-data-set="isDataSet"
                @openDialog="openDialog"
                @openDialogAsset="openDialogAsset"
                @getData="getData"
                @openDialogLastResv="openDialogLastResv"
                @openDeleteRow="openDeleteRow"
                @openAttachmentDetails="openAttachmentDetails"
                @openDialogInbound="openDialogInbound"
                @openDialogShipment="openDialogShipment"
                @closeDialog="closeDialog"
                @checkCount="checkCount"
              ></LazyResvReservationDetails>
            </div>

            <v-col cols="12" sm="12">
              <v-card-actions class="padding-15">
                <v-tooltip top style="margin-top: 4px">
                  <template v-slot:activator="{ props }">
                    <v-btn
                      size="small"
                      density="compact"
                      v-bind="props"
                      :disabled="isDisable"
                      color="primary darken-1"
                      class="white--text"
                      @click="addLine()"
                    >
                      <v-icon size="small" density="compact"> mdi-plus </v-icon>
                    </v-btn>
                  </template>
                  <span>Add Line</span>
                </v-tooltip>

                <!--                <v-tooltip-->
                <!--                  v-if='$route.query.status !== "add"'-->
                <!--                  top-->
                <!--                  class='ml-2'-->
                <!--                  style='margin-top: 4px'-->
                <!--                >-->
                <!--                  <template #activator='{ on, attrs }'>-->
                <!--                    <v-badge-->
                <!--                      :disabled='$route.query.status === "add"'-->
                <!--                      :content='count_attachment'-->
                <!--                      color='green'-->
                <!--                      :value='count_attachment'-->
                <!--                      overlap-->
                <!--                    >-->
                <!--                      <v-btn-->
                <!--                       size="small"
density="compact"-->
                <!--                        v-bind='attrs'-->
                <!--                        color='warning darken-1'-->
                <!--                        class='white&#45;&#45;text ml-2'-->
                <!--                        @click='openAttachment()'-->
                <!--                        v-on='on'-->
                <!--                      >-->
                <!--                        <v-icon size="small"
density="compact">-->
                <!--                          mdi-attachment-->
                <!--                        </v-icon>-->
                <!--                      </v-btn>-->
                <!--                    </v-badge>-->
                <!--                  </template>-->
                <!--                  <span>Add Attachment</span>-->
                <!--                </v-tooltip>-->

                <v-row wrap>
                  <v-col cols="12" md="10" class="pa-2">
                    <v-row no-gutters class="pt-1">
                      <v-col cols="1">
                        <label>Notes</label>
                      </v-col>
                      <v-col cols="11">
                        <v-text-field
                          v-model="form.Memo"
                          placeholder="Notes"
                          variant="outlined"
                          density="compact"
                          hide-details="auto"
                        ></v-text-field>
                      </v-col>
                    </v-row>
                  </v-col>
                </v-row>

                <v-spacer></v-spacer>

                <v-btn
                  size="small"
                  variant="flat"
                  :loading="submitLoad"
                  elevation="2"
                  :disabled="isDisable"
                  color="blue"
                  class="white--text"
                  @click="save('all', false)"
                >
                  <v-icon left> mdi-file-document </v-icon>
                  {{
                    $route.query.status === "add"
                      ? "Save As Draft"
                      : "Update Draft"
                  }}
                </v-btn>

                <v-tooltip
                  v-if="$route.query.status === 'update'"
                  top
                  style="margin-top: 4px"
                >
                  <template v-slot:activator="{ props }">
                    <v-btn
                      v-if="$route.query.status === 'update'"
                      size="small"
                      density="compact"
                      v-bind="props"
                      elevation="3"
                      :disabled="isDisable"
                      color="success"
                      class="white--text ml-1"
                      @click="dialogConfirm = true"
                    >
                      <v-icon size="small" density="compact">
                        mdi-check
                      </v-icon>
                    </v-btn>
                  </template>
                  <span>Submit For Approval</span>
                </v-tooltip>

                <v-tooltip top style="margin-top: 4px; margin-left: 4px">
                  <template v-slot:activator="{ props }">
                    <v-btn
                      size="small"
                      density="compact"
                      v-bind="props"
                      :loading="submitLoad3"
                      elevation="4"
                      :disabled="isDisable"
                      color="error darken-1"
                      class="white--text ml-1"
                      @click="deleteAll()"
                    >
                      <v-icon size="small" density="compact">
                        mdi-delete
                      </v-icon>
                    </v-btn>
                  </template>
                  <span>Delete All</span>
                </v-tooltip>
              </v-card-actions>
            </v-col>
          </v-row>
        </v-form>
      </v-card>

      <v-dialog v-model="dialogConfirm" persistent max-width="290">
        <v-card>
          <v-card-title class="headline"> Submit for approval? </v-card-title>
          <v-card-text>
            Are you sure you want to submit for approval?
          </v-card-text>
          <v-card-actions>
            <v-spacer></v-spacer>
            <v-btn
              color="error darken-1"
              text
              size="small"
              density="compact"
              @click="dialogConfirm = false"
            >
              No
            </v-btn>
            <v-btn
              color="primary darken-1"
              size="small"
              density="compact"
              dark
              :loading="submitLoad3"
              @click="save('all', true)"
            >
              Yes, Submit
            </v-btn>
          </v-card-actions>
        </v-card>
      </v-dialog>
    </v-col>

    <LazySnackbar ref="snackbar"></LazySnackbar>

    <LazyResvItemMasterData
      ref="itemsMaster"
      :form="form"
      @selectItems="selectItems"
    ></LazyResvItemMasterData>

    <LazyResvAssetMasterData
      ref="assetsMaster"
      :form="form"
      @selectAssets="selectAssets"
    ></LazyResvAssetMasterData>

    <LazyResvLastResv ref="lastResv" :form="form"></LazyResvLastResv>

    <LazyResvDialogDelete
      ref="dialogDelete"
      :url="urlDelete"
      @showMessage="showMessage"
    ></LazyResvDialogDelete>

    <LazySpinnerLoading
      v-if="dialogLoading"
      ref="spinnerLoadingImport"
    ></LazySpinnerLoading>

    <LazyAttachment
      ref="attachment"
      @eventCountAttachment="eventCountAttachment"
    ></LazyAttachment>

    <LazyResvInboundDialog
      ref="inboundDialog"
    ></LazyResvInboundDialog>

    <LazyResvShipmentDialog
      ref="shipmentDialog"
    ></LazyResvShipmentDialog>
  </v-row>
</template>

<script lang="ts">
export default {
  setup() {
    definePageMeta({
      middleware: "auth",
    });
  },
  name: "ReserVationForm",
  layout: "dialog",
  data() {
    return {
      dataTableOptions: {},
      itemsPerPage: 20,
      buttonKey: 1,
      buttonKey2: 2,
      buttonKey3: 3,
      count_attachment: 0,
      menu1: "",
      menu: "",
      date: null,
      urlDelete: null,
      dialogLoading: false,
      dialogConfirm: false,
      submitLoad: false,
      submitLoad2: false,
      submitLoad3: false,
      loading: false,
      loadingPrint: false,
      isReadOnly: false,
      loadingPrint2: false,
      message: false,
      success: false,
      itemDivision: [],
      itemUserNIK: [],
      itemCompany: [],
      itemWhTo: [],
      itemUserWhs: [],
      userDivision: [],
      breadcrumb: [],
      itemReqType: ["Normal", "For Restock SubWH"],
      ItemType: ["Ready Stock", "Non Ready Stock", "Asset", "Service"],
      isDisable: false,
      isApprovalDisable: false,
      title: "Reservation Form",
      isDataSet: false,
      Division: {},
      U_NIK: {},
      form: {
        Company: null,
        CompanyName: null,
        DocNum: null,
        UserName: null,
        U_NIK: this.$auth.user.username,
        RequesterName: this.$auth.user.name,
        Division: this.$auth.user.department,
        Department: null,
        DocDate: null,
        RequiredDate: null,
        WhsCode: null,
        RequestType: "Normal",
        ApprovalStatus: "-",
        U_DocEntry: null,
        LastApproved: null,
        Memo: null,
        Token: null,
        WhTo: null,
        UrgentReason: null,
        ItemType: null,
        ItemTypeCheck: null,
        Is_Urgent: "No",
      },
    };
  },

  head() {
    return {
      title: "Reservation Form",
    };
  },

  computed: {
    btnTitle() {
      return this.$route.query.status === "add"
        ? "Save As Draft"
        : "Save As Draft";
    },
  },

  created() {
    this.getUserRelationship();
    this.getData();
    this.getDivision();
  },

  mounted() {
    this.$nuxt.$emit("setTitle", this.title);
    setTimeout(() => {
      this.getData();
      this.getDivision();
      this.getUserRelationship();
    }, 1000);

    if (this.$auth.user.is_admin_subwh === "N") {
      this.itemReqType = ["Normal"];
    }
  },

  methods: {
    updateOptions(params: any) {
      this.dataTableOptions = params;
      this.getData();
    },

    changeDate() {
      // eslint-disable-next-line no-extend-native
      Date.prototype.addDays = function (days) {
        const date = new Date(this.valueOf());
        date.setDate(date.getDate() + days);
        return date;
      };

      // Add 7 Days
      const date = new Date(this.form.DocDate);
      this.form.RequiredDate = this.formatDate(date.addDays(7));
    },

    formatDate(date) {
      const d = new Date(date);
      let month = "" + (d.getMonth() + 1);
      let day = "" + d.getDate();
      const year = d.getFullYear();

      if (month.length < 2) month = "0" + month;
      if (day.length < 2) day = "0" + day;

      return [year, month, day].join("-");
    },

    getUserRelationship() {
      localStorage.setItem("request_type", JSON.stringify(["NPB", "SPB"]));

      this.$fetchData(`/api/master/users/relationship`, {
        params: {
          form: this.form,
        },
      }).then((res: any) => {
        this.itemCompany = res.items;
        this.itemUserNIK = res.userNik;
        this.itemUserWhs = res.itemUserWhs;
        this.userDivision = res.userDivision;
      });
    },

    getDivision() {
      this.$fetchData(`/api/master/division`).then((res: any) => {
        this.itemDivision = res.all_division;
      });
    },

    getWhsTo() {
      this.$fetchData(`/api/master/users/whsto`, {
        params: {
          form: this.form,
        },
      }).then((res: any) => {
        this.itemWhTo = res.itemWhTo;
      });
    },

    openDeleteRow(data) {
      this.urlDelete = data.url;
      this.$refs.dialogDelete.openDialog(data.url, data.row);
    },

    showMessage(message) {
      this.$refs.snackbar.display(10000, message);
      this.success = message;
      this.getData();
      setTimeout(() => (this.success = false), 8000);
    },

    openDialog(row) {
      if (this.form.CompanyName && this.form.WhsCode) {
        this.$refs.itemsMaster.openDialog(row);
      } else {
        this.$refs.snackbar.display(10000, "Company and WhsCode Cannot Empty!");
      }
    },

    openDialogAsset(row) {
      this.$refs.assetsMaster.openDialog(row);
    },

    openDialogLastResv(data) {
      if (this.form.CompanyName && this.form.WhsCode && this.form.DocDate) {
        // let itemCode = this.$refs.childDetails.getDataAtRowPropDetails(row, 'ItemCode')
        // let reqDate = this.$refs.childDetails.getDataAtRowPropDetails(row, 'ReqDate')
        // let whsCode = this.$refs.childDetails.getDataAtRowPropDetails(row, 'WhsCode')
        this.$refs.lastResv.openDialog(
          data.row,
          data.itemCode,
          data.reqDate,
          data.whsCode,
          data.itemName,
        );
      } else {
        this.$refs.snackbar.display(10000, "Company and WhsCode Cannot Empty!");
      }
    },

    closeDialog() {
      this.$refs.itemsMaster.closeDialog();
    },

    selectItems(data) {
      this.$refs.childDetails.insertDataToDetails(data.row, data.selected);
    },

    selectAssets(data) {
      this.$refs.childDetails.insertDataAssetToDetails(data.row, data.selected);
    },

    changeWhs() {
      this.$refs.childDetails.changeWhsDetails();
    },

    changeDivision() {
      const nik = this.form.U_NIK;
      // eslint-disable-next-line no-unused-vars
      let selectedDivision = "";
      const vm = this;
      this.userDivision.forEach(function (item) {
        if (item.U_NIK === nik) {
          selectedDivision = item.Division;
          vm.form.RequesterName = item.U_UserName;
          vm.form.Division = item.Division;
        }
      });
    },

    fillCheckbox() {
      this.$refs.childDetails.fillCheckboxDetails();
    },

    refreshData() {
      this.getData();
    },

    eventCountAttachment(data) {
      this.$refs.childDetails.setCountAttachment(data.total, data.row);
    },

    openAttachmentDetails(row) {
      this.$refs.attachment.openAttachment(
        row.lineEntry,
        "reservation",
        row.row,
      );
    },

    openAttachment() {
      this.$refs.attachment.openAttachment(this.$route.query.id, "reservation");
    },

    openDialogInbound(data) {
      const rowData = this.$refs.childDetails.getSourceDataAtRow(data.row);
      this.$refs.inboundDialog.openDialog(rowData, data.inboundNo);
    },

    openDialogShipment(data) {
      const rowData = this.$refs.childDetails.getSourceDataAtRow(data.row);
      this.$refs.shipmentDialog.openDialog(rowData, data.shipmentNo);
    },

    addLine() {
      const vm = this;
      if (
        this.form.CompanyName &&
        this.form.WhsCode &&
        this.form.RequestType &&
        this.form.DocDate &&
        this.form.ItemType
      ) {
        vm.$refs.childDetails.addLine();
        if (this.$refs.childDetails.countDetailsRows() > 1) {
          this.isReadOnly = true;
        }
      } else {
        this.$refs.snackbar.display(
          10000,
          "Company, Request Type, Request Date, Item Type and WhsCode Cannot Empty!",
        );
      }
    },

    checkCount() {
      if (this.$refs.childDetails.countDetailsRows() > 1) {
        this.isReadOnly = true;
      }
    },

    deleteAll() {
      const id = this.$route.query.id;
      const status = this.$route.query.status;
      if (status === "update") {
        const url = "/api/reservation/delete-all/" + id;
        this.$refs.dialogDelete.openDialog(url);
      }
    },

    save(type = "all", approval = false) {
      const vm = this;
      const form = this.form;
      const clearData = {};
      const status = this.$route.query.status;
      vm.submitLoad = true;
      const details = vm.$refs.childDetails.getAddData();
      details.forEach(function (item, key) {
        if (!vm.$refs.childDetails.checkIfEmptyRow(key)) clearData[key] = item;
      });

      const data = {
        form,
        details: clearData,
        type,
        status,
        Division: this.Division,
        U_NIK: this.U_NIK,
        approval,
      };

      if (status === "add") {
        this.store("post", "/api/reservation/master", data, "insert", type);
        vm.submitLoad = false;
      } else if (status === "update") {
        this.store(
          "put",
          "/api/reservation/master/" + this.form.U_DocEntry,
          data,
          "update",
          type,
        );
        vm.submitLoad = false;
      } else {
        this.$router.push({ name: "reservation" });
        vm.submitLoad = false;
      }
      vm.submitLoad = false;
    },

    store(method, url, data, type, column = "all") {
      const vm = this;
      vm.submitLoad = true;
      vm.isDisable = true;
      this.dialogLoading = true;
      this.$fetchData(url, { method, body: data })
        .then((res: any) => {
          this.dialogConfirm = false;
          this.$swal.fire({
            icon: "success",
            title: "Success!",
            text: res.message,
          });

          this.success = res.message;
          vm.$refs.snackbar.display(10000, res.message);
          vm.submitLoad = false;
          vm.isDisable = false;

          setTimeout(() => (this.success = false), 8000);
          this.dialogLoading = false;

          setTimeout(() => {
            vm.getData(res.data.U_DocEntry, "update");
          }, 500);

          this.$router.push({
            path: "/reservation/form",
            query: { id: res.data.U_DocEntry, status: "update" },
          });
        })
        // eslint-disable-next-line node/handle-callback-err
        .catch((err: any) => {
          this.dialogLoading = false;
          vm.submitLoad = false;
          vm.isDisable = false;
          this.$swal.fire({
            icon: "error",
            title: "Error",
            text: err.data.message,
          });
        });
    },

    getData(docEntry = null, statusUrl = null) {
      // eslint-disable-next-line no-unused-vars
      const vm = this;
      const id = docEntry || this.$route.query.id;
      const status = statusUrl || this.$route.query.status;
      const entry =
        this.$route.query.entry !== undefined ? this.$route.query.entry : 0;
      this.isApprovalDisable = this.$route.query.status === "add";

      if (status === "update") {
        this.dialogLoading = true;
        this.$fetchData(`/api/reservation/master/${id}`, {
          params: {
            entry,
          },
        })
          .then((res: any) => {
            this.count_attachment = res.count_attachment;
            // Fill Header
            if (res.header !== undefined) {
              this.form = Object.assign({}, res.header);
              this.Division = Object.assign({}, res.division);
              this.U_NIK = Object.assign({}, res.user_nik);

              if (
                this.form.ApprovalStatus !== "-" &&
                this.form.ApprovalStatus !== "N"
              ) {
                this.isDisable = true;
                this.isApprovalDisable = true;
              }

              if (
                parseInt(res.header.CreatedBy) !==
                parseInt(this.$auth.user.username)
              ) {
                this.isDisable = true;
                this.isApprovalDisable = true;
              }

              if (res.header.AppStatus === "Reject") {
                this.isDisable = true;
                this.isApprovalDisable = true;
              }
            }

            // Fill details
            if (res.rows !== undefined) {
              this.$refs.childDetails.setDataToDetails(res.rows);
            }

            this.breadcrumb = [
              {
                title: "Dashboard",
                disabled: false,
                to: {
                  path: "/",
                },
              },
              {
                title: "Reservation",
                disabled: false,
                to: {
                  path: "/reservation/request",
                },
              },
              {
                text: this.form.DocNum,
                disabled: true,
                to: {
                  path: "/reservation/form",
                  query: {
                    id: this.$route.query.id,
                    status: this.$route.query.status,
                  },
                },
              },
            ];
            this.dialogLoading = false;
            this.$refs.snackbar.display(6000, "Data loaded...");
          })
          .catch((err: any) => {
            this.dialogLoading = false;
            this.$swal.fire({
              icon: "error",
              title: "Error",
              text: err.data.message,
            });
          });
      } else {
        this.breadcrumb = [
          {
            title: "Dashboard",
            disabled: false,
            to: {
              path: "/",
            },
          },
          {
            title: "Reservation",
            disabled: false,
            to: {
              path: "/reservation/request",
            },
          },
          {
            title: "Create Reservation",
            disabled: true,
            to: {
              path: "/reservation/form",
            },
          },
        ];

        this.$fetchData(`/api/reservation/fetch-docnum`).then((res: any) => {
          this.form.DocNum = res.DocNum;
          this.form.Token = res.token;
          this.$refs.childDetails.updateTableSettings();
        });
      }
    },

    printDocument(type) {
      const vm = this;
      if (type === "all") {
        this.loadingPrint2 = true;
      } else {
        this.loadingPrint = true;
      }
      this.$fetchData(`/api/reservation/print`, {
        params: {
          form: vm.form,
          type,
        },
      })
        .then((res: any) => {
          window.open(res.message, "_blank");
          if (type === "all") {
            this.loadingPrint2 = false;
          } else {
            this.loadingPrint = false;
          }
        })
        // eslint-disable-next-line node/handle-callback-err
        .catch((err: any) => {
          if (type === "all") {
            this.loadingPrint2 = false;
          } else {
            this.loadingPrint = false;
          }
          this.$swal.fire({
            icon: "error",
            title: "Error",
            text: err.data.message,
          });
        });
    },
  },
};
</script>
