<template>
  <DialogForm
    ref="dialogForm"
    :max-width="700"
    :dialog-title="dialogTitle"
  >
    <template #content>
      <div class="pa-4">
        <v-card v-if="shipmentData" class="mb-4">
          <v-card-title class="text-h6 secondary white--text">
            <v-icon left color="white">mdi-truck</v-icon>
            Shipment Details - {{ shipmentData.SHIPMENT_NO }}
          </v-card-title>
          <v-card-text>
            <v-row>
              <v-col cols="12" md="6">
                <v-list dense>
                  <v-list-item>
                    <v-list-item-content>
                      <v-list-item-title class="font-weight-bold">Shipment No:</v-list-item-title>
                      <v-list-item-subtitle>{{ shipmentData.SHIPMENT_NO || '-' }}</v-list-item-subtitle>
                    </v-list-item-content>
                  </v-list-item>
                  <v-list-item>
                    <v-list-item-content>
                      <v-list-item-title class="font-weight-bold">Shipment Item:</v-list-item-title>
                      <v-list-item-subtitle>{{ shipmentData.SHIPMENT_ITEM || '-' }}</v-list-item-subtitle>
                    </v-list-item-content>
                  </v-list-item>
                  <v-list-item>
                    <v-list-item-content>
                      <v-list-item-title class="font-weight-bold">Shipment Date:</v-list-item-title>
                      <v-list-item-subtitle>{{ formatDate(shipmentData.SHIPMENT_DATE) || '-' }}</v-list-item-subtitle>
                    </v-list-item-content>
                  </v-list-item>
                  <v-list-item>
                    <v-list-item-content>
                      <v-list-item-title class="font-weight-bold">Sender:</v-list-item-title>
                      <v-list-item-subtitle>{{ shipmentData.PENGIRIM || '-' }}</v-list-item-subtitle>
                    </v-list-item-content>
                  </v-list-item>
                </v-list>
              </v-col>
              <v-col cols="12" md="6">
                <v-list dense>
                  <v-list-item>
                    <v-list-item-content>
                      <v-list-item-title class="font-weight-bold">Status:</v-list-item-title>
                      <v-list-item-subtitle>
                        <v-chip
                          :color="getStatusColor(shipmentData.STATUS)"
                          small
                          text-color="white"
                        >
                          {{ shipmentData.STATUS || 'Unknown' }}
                        </v-chip>
                      </v-list-item-subtitle>
                    </v-list-item-content>
                  </v-list-item>
                  <v-list-item>
                    <v-list-item-content>
                      <v-list-item-title class="font-weight-bold">Route:</v-list-item-title>
                      <v-list-item-subtitle>{{ shipmentData.ROUTE || '-' }}</v-list-item-subtitle>
                    </v-list-item-content>
                  </v-list-item>
                  <v-list-item>
                    <v-list-item-content>
                      <v-list-item-title class="font-weight-bold">Route Type:</v-list-item-title>
                      <v-list-item-subtitle>{{ shipmentData.ROUTE_TYPE || '-' }}</v-list-item-subtitle>
                    </v-list-item-content>
                  </v-list-item>
                </v-list>
              </v-col>
            </v-row>
          </v-card-text>
        </v-card>

        <!-- Related Inbound Information -->
        <v-card v-if="relatedInbound" class="mb-4">
          <v-card-title class="text-h6 primary white--text">
            <v-icon left color="white">mdi-package-variant</v-icon>
            Related Inbound - {{ relatedInbound.INBOUND_NO }}
          </v-card-title>
          <v-card-text>
            <v-row>
              <v-col cols="12" md="6">
                <v-list dense>
                  <v-list-item>
                    <v-list-item-content>
                      <v-list-item-title class="font-weight-bold">Inbound No:</v-list-item-title>
                      <v-list-item-subtitle>{{ relatedInbound.INBOUND_NO || '-' }}</v-list-item-subtitle>
                    </v-list-item-content>
                  </v-list-item>
                  <v-list-item>
                    <v-list-item-content>
                      <v-list-item-title class="font-weight-bold">PO No:</v-list-item-title>
                      <v-list-item-subtitle>{{ relatedInbound.PO_NO || '-' }}</v-list-item-subtitle>
                    </v-list-item-content>
                  </v-list-item>
                </v-list>
              </v-col>
              <v-col cols="12" md="6">
                <v-list dense>
                  <v-list-item>
                    <v-list-item-content>
                      <v-list-item-title class="font-weight-bold">Doc Date:</v-list-item-title>
                      <v-list-item-subtitle>{{ formatDate(relatedInbound.DOC_DATE) || '-' }}</v-list-item-subtitle>
                    </v-list-item-content>
                  </v-list-item>
                  <v-list-item>
                    <v-list-item-content>
                      <v-list-item-title class="font-weight-bold">Delivery Date:</v-list-item-title>
                      <v-list-item-subtitle>{{ formatDate(relatedInbound.DELIV_DATE) || '-' }}</v-list-item-subtitle>
                    </v-list-item-content>
                  </v-list-item>
                </v-list>
              </v-col>
            </v-row>
          </v-card-text>
        </v-card>

        <v-alert v-if="!shipmentData" type="warning" class="mt-4">
          No shipment data found for the selected item.
        </v-alert>
      </div>
    </template>
  </DialogForm>
</template>

<script lang="ts">
export default {
  name: "ShipmentDialog",
  data() {
    return {
      dialogTitle: "Shipment Details",
      shipmentData: null,
      relatedInbound: null,
    };
  },
  methods: {
    openDialog(rowData, shipmentNo) {
      this.dialogTitle = `Shipment Details - ${shipmentNo}`;
      this.shipmentData = null;
      this.relatedInbound = null;
      
      // Find the shipment data from DATA_INBOUND array
      if (rowData && rowData.DATA_INBOUND && Array.isArray(rowData.DATA_INBOUND)) {
        for (const inbound of rowData.DATA_INBOUND) {
          if (inbound.DATA_SHIPMENT && Array.isArray(inbound.DATA_SHIPMENT)) {
            const foundShipment = inbound.DATA_SHIPMENT.find(
              shipment => shipment.SHIPMENT_NO === shipmentNo
            );
            if (foundShipment) {
              this.shipmentData = foundShipment;
              this.relatedInbound = inbound;
              break;
            }
          }
        }
      }
      
      this.$refs.dialogForm.openDialog();
    },

    closeDialog() {
      this.$refs.dialogForm.closeDialog();
    },

    formatDate(dateString) {
      if (!dateString) return '-';
      try {
        const date = new Date(dateString);
        return date.toLocaleDateString('en-GB', {
          day: '2-digit',
          month: '2-digit',
          year: 'numeric'
        });
      } catch (error) {
        return dateString;
      }
    },

    getStatusColor(status) {
      switch (status?.toUpperCase()) {
        case 'COMPLETED':
        case 'DELIVERED':
          return 'success';
        case 'IN-PROCESS':
        case 'PROCESSING':
          return 'warning';
        case 'PENDING':
          return 'info';
        case 'CANCELLED':
        case 'FAILED':
          return 'error';
        default:
          return 'grey';
      }
    },
  },
};
</script>

<style scoped>
.v-list-item-title {
  font-size: 0.875rem;
}
.v-list-item-subtitle {
  font-size: 0.875rem;
  color: rgba(0, 0, 0, 0.87) !important;
}
</style>
