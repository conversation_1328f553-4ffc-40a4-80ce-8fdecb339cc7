<template>
  <HotTable ref="details" :root="detailsRoot" :settings="settings"></HotTable>
</template>

<script lang="ts">
import { HotTable } from "@handsontable/vue3";
import Handsontable from "handsontable";
import "handsontable/dist/handsontable.full.css";
import { registerAllModules } from "handsontable/registry";

registerAllModules();

Handsontable.renderers.registerRenderer(
  "AssetCodeRender",
  function (hotInstance, td, row, column, prop, value, cellProperties) {
    let button = null;
    const vm = window.details;
    const itemGroup = vm.$refs.details.hotInstance.getDataAtRowProp(
      row,
      "ItemGroup",
    );
    // console.log(itemGroup);
    // console.log(vm.itemType);
    // if (itemGroup === "ZAST" || vm.itemType === "Asset") {

    // }
    button = document.createElement("button");
    button.type = "button";
    button.innerText = "⮞";
    button.className = "btnSPB";
    button.value = "Details";

    Handsontable.dom.addEvent(button, "mousedown", (event) => {
      event.preventDefault();
      vm.$emit("openDialogAsset", {
        row,
      });
      // vm.$refs.inv.open()
    });

    Handsontable.dom.empty(td);
    td.appendChild(button);
    return td;
  },
);

Handsontable.renderers.registerRenderer(
  "UomRenderer",
  function (hotInstance, td, row, column, prop, value, cellProperties) {
    let button = null;
    const vm = window.details;

    button = document.createElement("button");
    button.type = "button";
    button.innerText = "⮞";
    button.className = "btnSPB";
    button.value = "Details";

    Handsontable.dom.addEvent(button, "mousedown", (event) => {
      event.preventDefault();
      vm.$emit("openDialogUom", {
        row,
      });
      // vm.$refs.inv.open()
    });

    Handsontable.dom.empty(td);
    td.appendChild(button);
    return td;
  },
);

Handsontable.renderers.registerRenderer(
  "OrderIdRender",
  function (hotInstance, td, row, column, prop, value, cellProperties) {
    let button = null;
    const vm = window.details;

    button = document.createElement("button");
    button.type = "button";
    button.innerText = "⮞";
    button.className = "btnSPB";
    button.value = "Details";

    Handsontable.dom.addEvent(button, "mousedown", (event) => {
      event.preventDefault();
      vm.$emit("openDialogOrderId", {
        row,
      });
      // vm.$refs.inv.open()
    });

    Handsontable.dom.empty(td);
    td.appendChild(button);
    return td;
  },
);

Handsontable.renderers.registerRenderer(
  "AttachmentRender",
  function (hotInstance, td, row, column, prop, value, cellProperties) {
    let button = null;
    const vm = window.details;
    const docEntry = vm.$refs.details.hotInstance.getDataAtRowProp(
      row,
      "LineEntry",
    );
    if (docEntry) {
      const cntAttachment = parseInt(vm.countAttachment);
      let countAttachment = parseInt(
        vm.$refs.details.hotInstance.getDataAtRowProp(row, "CountAttachment"),
      );
      if (cntAttachment > 0) {
        countAttachment = cntAttachment;
      }
      button = document.createElement("button");
      button.type = "button";
      button.innerText =
        docEntry && countAttachment > 0
          ? "✅ Attachment " + "(" + countAttachment + ")"
          : countAttachment > 0
            ? "👍 Attachment " + "(" + countAttachment + ")"
            : "⌛ Attachment";
      button.className =
        docEntry && countAttachment > 0
          ? "btnSPB row-" + row
          : countAttachment > 0
            ? "btnSPB row-" + row
            : "myBtRed";
      button.value = "Attachment";
      value = "test value attachment";
      // button.onclick = this.doIt
      Handsontable.dom.addEvent(button, "mousedown", function (event) {
        event.preventDefault();
        const lineEntry = vm.$refs.details.hotInstance.getDataAtRowProp(
          row,
          "LineEntry",
        );
        vm.$emit("openAttachmentDetails", {
          lineEntry,
          row,
        });
      });

      Handsontable.dom.empty(td);
      td.appendChild(button);
    }
    return td;
  },
);

export default {
  name: "ReservationDetailRow",
  components: {
    HotTable,
  },
  props: {
    form: {
      type: Object,
      // eslint-disable-next-line vue/require-valid-default-prop
      default: false,
    },
    isDataSet: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      dataTableOptions: {},
      itemsPerPage: 20,
      countAttachment: 0,
      detailsRoot: "detailsRoot",
      itemOrderId: [],
      itemType: null,
      settings: {
        licenseKey: "non-commercial-and-evaluation",
        startRows: 0,
      },
    };
  },

  computed: {
    checkIsDataSet() {
      return this.isDataSet;
    },
  },

  watch: {
    checkIsDataSet() {
      if (this.checkIsDataSet) {
        const data = JSON.parse(localStorage.getItem("approvalDetails"));
        this.setDataToDetails(data);
      }
    },
  },

  created() {
    this.instance();
  },

  methods: {
    updateOptions(params: any) {
      this.dataTableOptions = params;
      this.getData();
    },

    instance() {
      window.details = this;
    },

    countDetailsRows() {
      return this.$refs.details.hotInstance.countRows();
    },

    render() {
      this.$refs.details.hotInstance.render();
    },

    exportCsv() {
      const exportPlugin =
        this.$refs.details.hotInstance.getPlugin("exportFile");
      exportPlugin.downloadFile("csv", {
        bom: false,
        columnDelimiter: ",",
        columnHeaders: true,
        exportHiddenColumns: false,
        exportHiddenRows: false,
        fileExtension: "csv",
        filename: "export-file_[YYYY]-[MM]-[DD]",
        mimeType: "text/csv",
        rowDelimiter: "\r\n",
        rowHeaders: true,
      });
    },

    addLine() {
      const vm = this;
      const totalRow = this.$refs.details.hotInstance.countRows();
      const documentType = this.form.DocumentType;
      this.$refs.details.hotInstance.alter("insert_row_below", totalRow + 1);
      this.$refs.details.hotInstance.setDataAtRowProp([
        // updated 26-06-2020 from 50 to 51
        [totalRow, "ReqDate", vm.form.DocDate],
        // updated 26-06-2020 from 41 to 42
        [totalRow, "WhsCode", vm.form.WhsCode],
      ]);
      if (documentType === "Service") {
        this.$refs.details.hotInstance.setDataAtRowProp([
          [totalRow, "SPB", "Y"],
        ]);
      }
    },

    changeWhsDetails() {
      const vm = this;
      const countDetails = this.$refs.details.hotInstance.countRows();
      for (let j = 0; j < countDetails; j++) {
        vm.$refs.details.hotInstance.setDataAtRowProp([
          [j, "WhsCode", vm.form.WhsCode],
          [j, "ReqDate", vm.form.DocDate],
        ]);
      }
    },

    getAddData() {
      return this.$refs.details.hotInstance.getSourceData();
      // return this.$refs.details.hotInstance.getData()
    },

    checkIfEmptyRow(key) {
      return this.$refs.details.hotInstance.isEmptyRow(key);
    },

    fillCheckboxDetails2() {
      const vm = this;
      const countDetails = this.$refs.details.hotInstance.countRows();
      const reqType = vm.form.RequestType;
      let spbVal = "N";
      let npbVal = "N";
      for (let j = 0; j < countDetails; j++) {
        vm.$refs.details.hotInstance.setDataAtRowProp([
          [j, "NPB", "Y"],
          [j, "SPB", "N"],
        ]);
      }
    },

    fillCheckboxDetails() {
      const vm = this;
      const countDetails = this.$refs.details.hotInstance.countRows();
      const reqType = vm.form.RequestType;
      let spbVal = "N";
      let npbVal = "N";
      for (let j = 0; j < countDetails; j++) {
        vm.$refs.details.hotInstance.setDataAtRowProp([
          [j, "NPB", "N"],
          [j, "SPB", "N"],
        ]);
      }

      for (let j = 0; j < countDetails; j++) {
        const itemCategory = this.$refs.details.hotInstance.getDataAtRowProp(
          j,
          "ItemCategory",
        );
        if (reqType === "Normal" && itemCategory === "RS") {
          npbVal = "Y";
          spbVal = "N";
        } else if (reqType === "Normal" && itemCategory === "NRS") {
          spbVal = "Y";
          npbVal = "N";
        } else if (reqType === "For Restock SubWH" && itemCategory === "RS") {
          npbVal = "Y";
          spbVal = "N";
        } else if (reqType === "For Restock SubWH" && itemCategory === "NRS") {
          spbVal = "Y";
          npbVal = "N";
        }
        vm.$refs.details.hotInstance.setDataAtRowProp([
          [j, "NPB", npbVal],
          [j, "SPB", spbVal],
        ]);
      }
    },

    getDataAtRowPropDetails(row, prop) {
      return this.$refs.details.hotInstance.getDataAtRowProp(row, prop);
    },

    insertDataAssetToDetails(row, selected) {
      let rowData = row.row;
      const vm = this;
      selected.forEach(function (item, index) {
        vm.$refs.details.hotInstance.setDataAtRowProp([
          [rowData, "AssetCode", item.ItemCode],
          [rowData, "AssetName", item.ItemName],
        ]);
        rowData++;
      });
    },

    insertDataOrderToDetails(row, selected) {
      let rowData = row.row;
      const vm = this;
      selected.forEach(function (item, index) {
        vm.$refs.details.hotInstance.setDataAtRowProp([
          [rowData, "OrderId", item.ORDER],
        ]);
        rowData++;
      });
    },

    insertDataUomToDetails(row, selected) {
      let rowData = row.row;
      const vm = this;
      selected.forEach(function (item, index) {
        vm.$refs.details.hotInstance.setDataAtRowProp([
          [rowData, "UoMCode", item.MSEHI],
        ]);
        rowData++;
      });
    },

    insertDataToDetails(row, selected) {
      let rowData = row.row;
      const vm = this;
      // console.log(selected);
      selected.forEach(function (item, index) {
        // console.log(item);
        vm.$fetchData(`/api/latest-req-item`, {
          params: {
            ReqDate: vm.form.DocDate,
            WhsCode: vm.form.WhsCode,
            ItemCode: item.ItemCode,
          },
        }).then((res: any) => {
          const itemCategory = item.U_ItemType ? item.U_ItemType : "RS";
          const reqType = vm.form.RequestType;
          let spbVal = "N";
          let npbVal = "N";

          if (reqType === "Normal" && itemCategory === "RS") {
            npbVal = "Y";
          } else if (reqType === "Urgent" && itemCategory === "RS") {
            npbVal = "Y";
          } else if (reqType === "Normal" && itemCategory === "CRC") {
            npbVal = "Y";
          } else if ((reqType === "Normal" || reqType === "Project") && itemCategory === "NRS") {
            spbVal = "Y";
          } else if (reqType === "Normal" && itemCategory === "ASSET") {
            spbVal = "Y";
          } else if (reqType === "Urgent" && itemCategory === "NRS") {
            spbVal = "Y";
          } else if (reqType === "Urgent" && itemCategory === "ASSET") {
            spbVal = "Y";
          } else if (reqType === "For Restock SubWH" && itemCategory === "RS") {
            npbVal = "Y";
          } else if (
            reqType === "For Restock SubWH" &&
            itemCategory === "CRC"
          ) {
            npbVal = "Y";
          } else if (
            reqType === "For Restock SubWH" &&
            itemCategory === "NRS"
          ) {
            spbVal = "Y";
          } else if (reqType === "Restock" && itemCategory === "RS") {
            spbVal = "Y";
          }

          vm.$emit("checkCount");
          // console.log(item.InvntItem)
          vm.$refs.details.hotInstance.setDataAtRowProp([
            [rowData, "ItemCode", item.ItemCode],
            [rowData, "ItemGroup", item.ItmsGrpCod],
            [rowData, "SubGroup", item.U_SubGroup],
            [rowData, "ItemName", item.ItemName],
            [rowData, "UoMCode", item.InvntryUom],
            [rowData, "WhsCode", vm.form.WhsCode],
            [rowData, "ReqDate", vm.form.DocDate],
            [rowData, "OnHand", item.OnHand],
            [rowData, "AvailableQty", item.Available],
            [rowData, "ItemCategory", itemCategory],
            [rowData, "InvntItem", item.InvntItem],
            [rowData, "NPB", npbVal],
            [rowData, "SPB", spbVal],
            [rowData, "LastReqBy", res.rows ? res.rows.U_UserName : ""],
            // [
            //   rowData,
            //   'U_Department',
            //   item.U_Department != null ? item.U_Department : '-',
            // ],
            [rowData, "U_Period", item.U_Period != null ? item.U_Period : "-"],
            [
              rowData,
              "U_Category",
              item.U_Category != null ? item.U_Category : "-",
            ],
            [
              rowData,
              "U_AppResBy",
              item.U_AppResBy != null ? item.U_AppResBy : "-",
            ],
            [rowData, "NoGIR", item.NoGIR],
            [rowData, "NoPR", item.NoPR],
            [rowData, "NoPO", item.NoPO],
            [rowData, "INBOUND", item.INBOUND],
            [rowData, "SHIPMENT", item.SHIPMENT],
            [rowData, "NoGR", item.NoGR],
            [rowData, "NoGI", item.NoGI],
            // [
            //   rowData,
            //   'LastReqDate',
            //   res.rows ? res.rows.ReqDate : null,
            // ],
            // [
            //   rowData,
            //   'LastReqNote',
            //   res.rows ? res.rows.ReqNotes : null,
            // ],
            // [rowData, 'OIGRDocNum', item.DocEntry],
          ]);
          rowData++;
        });
      });
    },

    removeData(docEntry) {
      this.$fetchData(`/api/reservation/master/1`, {
        method: "delete",
        params: {
          doc_entry: docEntry,
        },
      })
        .then((res: any) => {
          this.$swal.fire({
            icon: "success",
            title: "Success!",
            text: res.message,
          });
          this.$emit("getData");
        })
        .catch((err: any) => {
          this.$swal.fire({
            icon: "error",
            title: "Error",
            text: err.data.message,
          });
          this.$emit("getData");
        });
    },

    setCountAttachment(total, row) {
      // console.log(total)
      // console.log(row)
      this.$refs.details.hotInstance.setDataAtRowProp(
        row,
        "CountAttachment",
        total,
      );
    },

    setDataToDetails(data, dataSchema) {
      setTimeout(() => {
        this.$refs.details.hotInstance.render();
        this.$refs.details.hotInstance.loadData(data);
        const documentType = this.form.DocumentType;
        const itemType = this.form.itemType;
        const columnHidden = this.changeColumnHidden(documentType, itemType);
        // switch (documentType) {
        //   case 'Service':
        //     // TODO uncomment 6,7,8,9,10
        //     columnHidden = [
        //       0, 1, 3, 4, 5, 8, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26,
        //       27, 28, 30, 31, 32, 33, 34, 35, 36, 37, 38,
        //     ]
        //     break
        //   default:
        //     columnHidden = [24, 25, 26, 27, 28, 30, 31, 32, 33, 34, 35]
        //     break
        // }
        this.updateTableSettings(
          columnHidden,
          dataSchema,
          documentType,
          itemType,
        );
        this.showLoadingInvDetail = false;
      }, 300);
    },

    changeColumnHidden(documentType, itemType) {
      // console.log(documentType);
      // console.log(this.itemType);
      if (documentType === "Service" && this.itemType === "Service") {
        return [
          0, 1, 3, 4, 5, 8, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27,
          28, 30, 31, 32, 33, 34, 35, 36, 37, 38,
        ];
      } else if (documentType === "Service" && this.itemType === "Asset") {
        return [
          0, 1, 8, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 30,
          31, 32, 33, 34, 35, 36, 37, 38,
        ];
      } else {
        return [24, 25, 26, 27, 28, 30, 31, 32, 33, 34, 35];
      }
    },

    changeTable(documentType, itemType, dataSchema) {
      if (itemType !== undefined) {
        this.itemType = itemType;
      }
      const columnHidden = this.changeColumnHidden(documentType, this.itemType);
      this.$refs.details.hotInstance.loadData([]);

      // switch (documentType) {
      //   case 'Service':
      //     // TODO uncomment 6,7,8,9,10
      //     columnHidden = [
      //       0, 1, 3, 4, 5, 8, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26,
      //         27, 28, 30, 31, 32, 33, 34, 35, 36, 37, 38,
      //     ]
      //     break
      //   default:
      //     columnHidden = [24, 25, 26, 27, 28, 30, 31, 32, 33, 34, 35]
      //     break
      // }
      // this.$refs.details.hotInstance.destroy();
      setTimeout(() => {
        this.updateTableSettings(
          columnHidden,
          dataSchema,
          documentType,
          this.itemType,
        );
      }, 500);
    },

    setIoOrder(data) {
      this.itemOrderId = data;
    },

    updateTableSettings(columnHidden, dataSchema, documentType, itemType) {
      // const vm = this
      // eslint-disable-next-line no-unused-vars
      const hot2 = this.$refs.details.hotInstance;
      this.$refs.details.hotInstance.render();
      // console.log(vm.itemOrderId)
      const itemNameWidth = documentType === "Service" ? 350 : 200;
      const stretchH =
        documentType === "Service" && itemType === "Service" ? "all" : "none";
      this.$refs.details.hotInstance.updateSettings({
        colHeaders: [
          "",
          "Item Code",
          "Item Name",
          "",
          "Asset Code",
          "Asset Name",
          "",
          "Order",
          "Category",
          "",
          "UoM",
          "WH",
          "Req. Qty",
          "Req. Date",
          "Notes",
          "OnHand",
          "Available",
          "Other Resv. No ",
          "SPB",
          "NPB",
          "Last Res. By",
          "Vehicle No",
          "Mileage",
          "Delete",
          "DocEntry",
          "LineStatus",
          "OIGRDocNum",
          "InvntItem",
          "GIR Status",
          "Attachment",
          "CountAttachment",
          "ItemGroup",
          "SubGroup",
          "U_Period",
          "U_Category",
          "U_AppResBy",
          "Employee NIK",
          "Employee NAme",
          "Department",
          "No GIR",
          "No PR",
          "No PO",
          "No INBOUND",
          "No SHIPMENT",
          "No GR",
          "No GI",
        ],
        copyable: true,
        copyPaste: true,
        columns: [
          {
            // 0
            data: "ItemCodeRender",
            width: 30,
            height: 26,
            wordWrap: false,
          },
          {
            // 1
            data: "ItemCode",
            width: 100,
            height: 26,
            wordWrap: false,
            readOnly: true,
          },
          {
            // 2
            data: "ItemName",
            width: 200,
            height: 26,
            wordWrap: false,
            // readOnly: true,
          },
          {
            // 3
            data: "AssetCodeRender",
            width: 30,
            height: 26,
            wordWrap: false,
            renderer: "AssetCodeRender",
          },
          {
            // 4
            data: "AssetCode",
            width: 100,
            height: 26,
            wordWrap: false,
            readOnly: true,
          },
          {
            // 5
            data: "AssetName",
            width: 200,
            height: 26,
            wordWrap: false,
            readOnly: true,
          },
          {
            // 6
            data: "OrderIdRender",
            width: 30,
            height: 26,
            wordWrap: false,
            renderer: "OrderIdRender",
          },
          // TODO
          {
            // 7
            data: "OrderId",
            width: 100,
            height: 26,
            wordWrap: false,
            readOnly: true,
          },
          {
            // 8
            data: "ItemCategory",
            width: 70,
            height: 26,
            wordWrap: false,
            readOnly: true,
          },
          {
            // 9
            data: "UomRenderer",
            width: 30,
            height: 26,
            wordWrap: false,
            renderer: "UomRenderer",
          },
          {
            // 10
            data: "UoMCode",
            width: 100,
            height: 26,
            wordWrap: false,
            readOnly: true,
          },
          {
            // 11
            data: "WhsCode",
            width: 60,
            height: 26,
            wordWrap: false,
            readOnly: true,
          },
          {
            // 12
            data: "ReqQty",
            width: 80,
            height: 26,
            wordWrap: false,
            type: "numeric",
            numericFormat: {
              pattern: "0,0.000",
            },
          },
          {
            // 13
            data: "ReqDate",
            type: "date",
            width: 100,
            height: 26,
            wordWrap: false,
            dateFormat: "YYYY-MM-DD",
            correctFormat: true,
            datePickerConfig: {
              firstDay: 0,
              showWeekNumber: true,
              numberOfMonths: 1,
            },
          },
          {
            // 14
            data: "ReqNotes",
            width: 250,
            height: 26,
            wordWrap: false,
            type: "autocomplete",
            strict: false,
            source(query: any, process: any) {
              const vm = window.details;
              const data = vm.$auth.$storage.getState("apdNotes");
              process(data);
            },
          },
          {
            // 15
            data: "OnHand",
            width: 80,
            height: 26,
            wordWrap: false,
            readOnly: true,
            type: "numeric",
            numericFormat: {
              pattern: "0,0.00",
            },
          },
          {
            // 16
            data: "AvailableQty",
            width: 80,
            height: 26,
            wordWrap: false,
            readOnly: true,
            type: "numeric",
            numericFormat: {
              pattern: "0,0.00",
            },
          },
          {
            // 17
            data: "OtherResvNo",
            width: 150,
            height: 26,
            wordWrap: false,
          },

          {
            // 18
            data: "SPB",
            width: 50,
            height: 26,
            wordWrap: false,
            className: "htCenter ",
            type: "checkbox",
            checkedTemplate: "Y",
            uncheckedTemplate: "N",
          },

          {
            // 19
            data: "NPB",
            width: 50,
            height: 26,
            wordWrap: false,
            className: "htCenter ",
            type: "checkbox",
            checkedTemplate: "Y",
            uncheckedTemplate: "N",
          },
          {
            // 20
            data: "LastReqBy",
            width: 150,
            height: 26,
            readOnly: true,
            wordWrap: false,
          },
          {
            // 21
            data: "VehicleNo",
            width: 100,
            height: 26,
            wordWrap: false,
            type: "dropdown",
            source(query: any, process: any) {
              const vm = window.details;
              const data = vm.$auth.$storage.getState("vehicle");
              process(data);
            },
          },
          {
            // 22
            data: "Mileage",
            width: 100,
            height: 26,
            wordWrap: false,
            type: "numeric",
            numericFormat: {
              pattern: "0,0.00",
            },
          },

          {
            // 23
            data: "Delete",
            width: 100,
            height: 26,
            wordWrap: false,
          },

          {
            // 24
            data: "LineEntry",
            width: 100,
            height: 26,
            wordWrap: false,
          },

          {
            // 25
            data: "LineStatus",
            width: 100,
            height: 26,
            wordWrap: false,
          },

          {
            // 26
            data: "OIGRDocNum",
            width: 100,
            height: 26,
            wordWrap: false,
          },
          {
            // 27
            data: "InvntItem",
            width: 100,
            height: 26,
            wordWrap: false,
          },
          {
            // 28
            data: "SAPGIRStatus",
            width: 100,
            height: 26,
            wordWrap: false,
          },
          {
            // 29
            data: "Attachment",
            width: 120,
            height: 26,
            wordWrap: false,
            renderer: "AttachmentRender",
          },
          {
            // 30
            data: "CountAttachment",
            width: 100,
            height: 26,
            wordWrap: false,
          },
          {
            // 31
            data: "ItemGroup",
            width: 100,
            height: 26,
            wordWrap: false,
          },
          {
            // 32
            data: "SubGroup",
            width: 100,
            height: 26,
            wordWrap: false,
          },
          {
            // 33
            data: "U_Period",
            width: 100,
            height: 26,
            wordWrap: false,
            type: "text",
          },
          {
            // 34
            data: "U_Category",
            width: 100,
            height: 26,
            wordWrap: false,
            type: "text",
          },
          {
            // 35
            data: "U_AppResBy",
            width: 100,
            height: 26,
            wordWrap: false,
            type: "text",
          },
          {
            // 36
            data: "EmployeeId",
            width: 100,
            height: 26,
            wordWrap: false,
            type: "text",
          },
          {
            // 37
            data: "EmployeeName",
            width: 170,
            height: 26,
            wordWrap: false,
            readOnly: true,
            type: "text",
          },
          {
            // 38
            data: "U_Department",
            width: 200,
            height: 26,
            wordWrap: false,
            type: "dropdown",
            source(query: any, process: any) {
              const vm = window.details;
              const data = vm.$auth.$storage.getState("department");
              process(data);
            },
            strict: true,
            filter: false,
            allowInvalid: false,
          },
          {
            // 39
            data: "NoGIR",
            width: 100,
            height: 26,
            wordWrap: false,
            type: "text",
          },
          {
            // 40
            data: "NoPR",
            width: 100,
            height: 26,
            wordWrap: false,
            type: "text",
          },
          {
            // 41
            data: "NoPO",
            width: 100,
            height: 26,
            wordWrap: false,
            type: "text",
          },
          {
            // 42
            data: "INBOUND",
            width: 100,
            height: 26,
            wordWrap: false,
            type: "text",
          },
          {
            // 43
            data: "SHIPMENT",
            width: 100,
            height: 26,
            wordWrap: false,
            type: "text",
          },
          {
            // 44
            data: "NoGR",
            width: 100,
            height: 26,
            wordWrap: false,
            type: "text",
          },
          {
            // 45
            data: "NoGI",
            width: 100,
            height: 26,
            wordWrap: false,
            type: "text",
          },
        ],
        currentRowClassName: "currentRow",
        currentColClassName: "currentCol",
        startRows: 0,
        manualColumnFreeze: true,
        currData: {},
        rowHeaders: true,
        manualColumnResize: true,
        manualRowResize: true,
        // filters: true,
        autoRowSize: false,
        autoColumnSize: false,
        viewportRowRenderingOffset: 1000,
        viewportColumnRenderingOffset: 100,
        // dropdownMenu: true,
        // columnSorting: true,
        rowHeights: 23,
        persistentState: true,
        width: "100%",
        stretchH,
        colWidths: 50,
        hiddenColumns: {
          copyPasteEnabled: false,
          indicator: false,
          columns: columnHidden,
        },
        dataSchema,

        contextMenu: {
          callback(key, options) {
            // eslint-disable-next-line no-unused-vars
            let indexArr, selectedData;
            const vm = window.details;
            // console.log(key)
            if (key === "remove_row") {
              vm.isInvDetailChanges = true;
              vm.isInvChanges = true;
            }
          },
        },
        // height: 400,
        beforeRemoveRow: (index, amount, physicalRow, source) => {
          const vm = window.details;
          const delQuestion = prompt(
            "Do you want to delete a row? Type y to remove",
          );
          const docEntry = [];
          if (delQuestion === "y") {
            physicalRow.forEach(function (index, value) {
              const entry = vm.$refs.details.hotInstance.getDataAtRowProp(
                index,
                "LineEntry",
              );
              if (entry) {
                docEntry.push(entry);
              }
            });
            if (docEntry.length > 0) {
              vm.removeData(docEntry);
            }
            return true;
          } else {
            return false;
          }
        },
        // beforeOnCellMouseDown: doNotSelectColumn,
        afterOnCellMouseDown(e, coords, TD, value) {
          const glob = window.details;
          const getCoor = glob.$refs.details.hotInstance.propToCol;
          const docEntry = glob.$refs.details.hotInstance.getDataAtRowProp(
            coords.row,
            "LineEntry",
          );

          if (coords.col === getCoor("ItemCodeRender")) {
            glob.$emit("openDialog", {
              row: coords.row,
              form: glob.form,
            });
          }

          if (coords.col === getCoor("LastReqBy")) {
            glob.$emit("openDialogLastResv", {
              row: coords.row,
              itemCode: glob.$refs.details.hotInstance.getDataAtRowProp(
                coords.row,
                "ItemCode",
              ),
              itemName: glob.$refs.details.hotInstance.getDataAtRowProp(
                coords.row,
                "ItemName",
              ),
              reqDate: glob.$refs.details.hotInstance.getDataAtRowProp(
                coords.row,
                "ReqDate",
              ),
              whsCode: glob.$refs.details.hotInstance.getDataAtRowProp(
                coords.row,
                "WhsCode",
              ),
            });
          }

          if (coords.col === getCoor("Delete")) {
            glob.$emit("openDeleteRow", {
              url: "/api/reservation/" + docEntry,
              row: coords.row,
            });
          }
        },

        afterOnCellMouseOut(e, coords, TD, value) {
          const glob = window.details;
          const getCoor = glob.$refs.details.hotInstance.propToCol;

          if (coords.col === getCoor("NPB")) {
            const npbVal = glob.$refs.details.hotInstance.getDataAtRowProp(
              coords.row,
              "NPB",
            );
            if (npbVal === "Y") {
              glob.$refs.details.hotInstance.setDataAtRowProp(
                coords.row,
                "SPB",
                "N",
              );
            }
          }

          if (coords.col === getCoor("SPB")) {
            const spbVal = glob.$refs.details.hotInstance.getDataAtRowProp(
              coords.row,
              "SPB",
            );
            if (spbVal === "Y") {
              glob.$refs.details.hotInstance.setDataAtRowProp([
                [coords.row, "NPB", "N"],
                [coords.row, "OtherResvNo", null],
              ]);
            }
          }
        },

        afterGetColHeader(col, TH) {
          if (typeof col !== "number") {
            return col;
          }

          const TR = TH.parentNode;
          const THEAD = TR.parentNode;
          // eslint-disable-next-line no-unused-vars
          const headerLevel =
            -1 * THEAD.childNodes.length +
            Array.prototype.indexOf.call(THEAD.childNodes, TR);

          // eslint-disable-next-line no-unused-vars
          function applyClass(elem, className) {
            if (!Handsontable.dom.hasClass(elem, className)) {
              Handsontable.dom.addClass(elem, className);
            }
          }
        },

        // afterChange: (changes, source) => {
        //   let vm = window.details
        //   if (changes) {
        //     let propNew = 0
        //     changes.forEach(([row, prop, oldValue, newValue]) => {
        //     })
        //   }
        // },
        // manualColumnFreeze: true,
        // fixedColumnsLeft: 5,
        cells(row, col, prop) {
          const cellProperties = {};
          const vm = window.details;
          const category = this.instance.getDataAtRowProp(row, "ItemCategory");
          const lineStatus = this.instance.getDataAtRowProp(row, "LineStatus");
          const invntItem = this.instance.getDataAtRowProp(row, "InvntItem");
          // const itemGroup = this.instance.getDataAtRowProp(row, 'ItemGroup')
          const npbVal = this.instance.getDataAtRowProp(row, "NPB");
          const approvalStatus = vm.form.ApprovalStatus;
          const documentType = vm.form.DocumentType;
          const requestType = vm.form.RequestType;
          let isNpbActive = true;

          if (lineStatus === "Closed") {
            for (let i = 0; i <= 38; i++) {
              if (col === i) {
                cellProperties.readOnly = true;
              }
            }
          } else if (approvalStatus !== "-" && approvalStatus !== "N") {
            // cellProperties.editor = false;
            for (let i = 0; i <= 38; i++) {
              if (col === i) {
                cellProperties.readOnly = true;
              }
            }
          } else {
            if (prop === "ItemName") {
              if (documentType === "Item") {
                cellProperties.readOnly = invntItem === "Y";
              } else {
                cellProperties.readOnly = false;
              }
            }

            // if (prop === 'UoMCode') {
            //   cellProperties.readOnly = !(
            //     itemGroup === '102' ||
            //     itemGroup === '152' ||
            //     documentType !== 'Item'
            //   )
            // }

            if (prop === "SPB") {
              if (documentType !== "Item") {
                cellProperties.readOnly = true;
              } else if (requestType === "Normal" && category === "RS") {
                if (
                  vm.$hasRole([
                    "Admin E-Resv Display All Item",
                    "Admin E-RESERVATION BDM",
                  ])
                ) {
                  cellProperties.readOnly = false;
                } else {
                  cellProperties.readOnly = true;
                }
              } else if (
                requestType === "For Restock SubWH" &&
                category === "RS"
              ) {
                cellProperties.readOnly = true;
              } else if (requestType === "Normal" && category === "CRC") {
                cellProperties.readOnly = true;
              } else if (
                requestType === "For Restock SubWH" &&
                category === "CRC"
              ) {
                cellProperties.readOnly = true;
              } else if (requestType === "Restock" && category === "RS") {
                cellProperties.readOnly = false;
              }
            }

            if (prop === "NPB") {
              if (requestType === "Normal" && category === "NRS") {
                // if (available_qty > 0) {
                //   cellProperties.editor = true;
                //   cellProperties.readOnly = false;
                //   isNpbActive = true
                // }
                // cellProperties.editor = true;
                cellProperties.readOnly = false;
                isNpbActive = true;
              } else if (
                requestType === "For Restock SubWH" &&
                category === "NRS"
              ) {
                // cellProperties.editor = false;
                cellProperties.readOnly = false;
                // eslint-disable-next-line no-unused-vars
                isNpbActive = true;
              } else if ((requestType === "Restock" || requestType === "Project") && category === "NRS") {
                cellProperties.readOnly = true;
              } else if (requestType === "Restock" && category === "RS") {
                cellProperties.readOnly = true;
              }
            }

            if (prop === "OtherResvNo") {
              cellProperties.readOnly = !(
                npbVal === "Y" &&
                (requestType === "Normal" || requestType === "Urgent")
              );
            }

            if (
              prop === "ItemCodeRender" ||
              prop === "LastReqBy" ||
              prop === "Delete"
            ) {
              let button = null;

              cellProperties.readOnly = true;
              cellProperties.renderer = function (
                instance,
                td,
                row,
                col,
                prop,
                value,
                cellProperties,
              ) {
                if (prop === "LastReqBy") {
                  button = document.createElement("a");
                  button.innerHTML = value;
                }

                if (prop === "ItemCodeRender" || prop === "Delete") {
                  button = document.createElement("button");
                  button.type = "button";
                  if (prop === "ItemCodeRender") {
                    button.innerText = "⮞";
                    button.className = "btnSPB";
                  }

                  if (prop === "Delete") {
                    button.innerText = "🗑️ Delete";
                    button.className = "btnDelete";
                  }
                }
                Handsontable.dom.empty(td);
                td.appendChild(button);
                return td;
              };
            }
          }
          return cellProperties;
        },
      });
    },
  },
};
</script>
